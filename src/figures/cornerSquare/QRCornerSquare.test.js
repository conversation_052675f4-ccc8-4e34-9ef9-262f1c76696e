/**
 * @jest-environment jsdom
 */
import QRCornerSquare from "./QRCornerSquare";
import cornerSquareTypes from "../../constants/cornerSquareTypes";

describe('QRCornerSquare', () => {
    let svg, cornerSquare;

    beforeEach(() => {
        document.body.innerHTML = '';
        svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
        svg.setAttribute("width", "300");
        svg.setAttribute("height", "300");
        document.body.appendChild(svg);
    });

    afterEach(() => {
        document.body.innerHTML = '';
    });

    test('should create corner square with dot type', () => {
        cornerSquare = new QRCornerSquare({
            svg,
            type: cornerSquareTypes.dot,
            window
        });

        expect(cornerSquare).toBeDefined();
        expect(cornerSquare._type).toBe(cornerSquareTypes.dot);
    });

    test('should draw dot pattern correctly', () => {
        cornerSquare = new QRCornerSquare({
            svg,
            type: cornerSquareTypes.dot,
            window
        });

        cornerSquare.draw(10, 10, 70, 0);

        expect(cornerSquare._element).toBeDefined();
        expect(cornerSquare._element.tagName).toBe('path'); // Back to single path element
        expect(cornerSquare._element.getAttribute('fill-rule')).toBe('evenodd');
        
        // Check if the path contains the expected circle patterns
        const pathD = cornerSquare._element.getAttribute('d');
        expect(pathD).toContain('A '); // Arc commands for circles
        expect(pathD).toContain('M '); // Move commands
        expect(pathD).toContain('Z'); // Close path commands
        
        // Should contain multiple circles (outer ring + center)
        const arcCount = (pathD.match(/A /g) || []).length;
        expect(arcCount).toBeGreaterThanOrEqual(4); // At least 4 arcs for 2+ complete circles
    });

    test('should draw square pattern correctly', () => {
        cornerSquare = new QRCornerSquare({
            svg,
            type: cornerSquareTypes.square,
            window
        });

        cornerSquare.draw(10, 10, 70, 0);

        expect(cornerSquare._element).toBeDefined();
        expect(cornerSquare._element.tagName).toBe('path');
        
        // Check if the path contains lines (characteristic of square patterns)
        const pathD = cornerSquare._element.getAttribute('d');
        expect(pathD).toContain('v '); // Vertical line commands
        expect(pathD).toContain('h '); // Horizontal line commands
    });

    test('should create different structures for dot vs square', () => {
        // Create dot pattern
        const dotCornerSquare = new QRCornerSquare({
            svg,
            type: cornerSquareTypes.dot,
            window
        });
        dotCornerSquare.draw(10, 10, 70, 0);

        // Create square pattern  
        const squareCornerSquare = new QRCornerSquare({
            svg,
            type: cornerSquareTypes.square,
            window
        });
        squareCornerSquare.draw(10, 10, 70, 0);

        // Both should be path elements now
        expect(dotCornerSquare._element.tagName).toBe('path'); // Path for dot
        expect(squareCornerSquare._element.tagName).toBe('path'); // Path for square
        
        // Dot pattern should contain arcs, square pattern should contain lines
        const dotPathD = dotCornerSquare._element.getAttribute('d');
        const squarePathD = squareCornerSquare._element.getAttribute('d');
        
        // Dot pattern should contain arcs
        expect(dotPathD).toContain('A ');
        
        // Square pattern should have lines
        expect(squarePathD).toContain('v '); // Vertical lines
        expect(squarePathD).toContain('h '); // Horizontal lines
        
        // Paths should be different
        expect(dotPathD).not.toBe(squarePathD);
    });

    test('should handle rotation parameter', () => {
        cornerSquare = new QRCornerSquare({
            svg,
            type: cornerSquareTypes.dot,
            window
        });

        const rotation = Math.PI / 2; // 90 degrees
        cornerSquare.draw(10, 10, 70, rotation);

        expect(cornerSquare._element).toBeDefined();
        
        // Check if transform attribute is set for rotation
        const transform = cornerSquare._element.getAttribute('transform');
        expect(transform).toContain('rotate(90');
    });

    test('should create path element in SVG', () => {
        cornerSquare = new QRCornerSquare({
            svg,
            type: cornerSquareTypes.dot,
            window
        });

        cornerSquare.draw(10, 10, 70, 0);

        expect(svg.contains(cornerSquare._element)).toBe(false); // Element is created but not automatically added to SVG
        expect(cornerSquare._element.ownerDocument).toBe(document);
    });

    test('should handle extra-rounded type', () => {
        cornerSquare = new QRCornerSquare({
            svg,
            type: cornerSquareTypes.extraRounded,
            window
        });

        cornerSquare.draw(10, 10, 70, 0);

        expect(cornerSquare._element).toBeDefined();
        expect(cornerSquare._element.tagName).toBe('path');
        
        // Extra-rounded should have specific arc characteristics
        const pathD = cornerSquare._element.getAttribute('d');
        expect(pathD).toContain('a ');
    });

    test('should validate available corner square types', () => {
        expect(cornerSquareTypes.dot).toBe('dot');
        expect(cornerSquareTypes.square).toBe('square'); 
        expect(cornerSquareTypes.extraRounded).toBe('extra-rounded');
    });

    test('should default to dot pattern for unknown type', () => {
        cornerSquare = new QRCornerSquare({
            svg,
            type: 'unknown-type',
            window
        });

        cornerSquare.draw(10, 10, 70, 0);

        // Should default to dot pattern (single path with arcs)
        expect(cornerSquare._element.tagName).toBe('path');
        expect(cornerSquare._element.getAttribute('fill-rule')).toBe('evenodd');
        
        // Should contain arcs like dot pattern
        const pathD = cornerSquare._element.getAttribute('d');
        expect(pathD).toContain('A '); // Should contain arcs
    });
});