<?xml version="1.0" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="300" height="300" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 300 300"><defs><clipPath id="clip-path-background-color-2"><rect x="0" y="0" width="300" height="300"/></clipPath><clipPath id="clip-path-dot-color-2"><path d="M 115 3v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(180,122,10)"/><path d="M 129 3v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(0,136,10)"/><circle cx="164" cy="10" r="7" transform="rotate(0,164,10)"/><circle cx="150" cy="24" r="7" transform="rotate(0,150,24)"/><circle cx="178" cy="24" r="7" transform="rotate(0,178,24)"/><path d="M 129 31v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(-90,136,38)"/><circle cx="164" cy="38" r="7" transform="rotate(0,164,38)"/><path d="M 129 45v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(180,136,52)"/><path d="M 143 45v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(0,150,52)"/><path d="M 115 59v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(-90,122,66)"/><path d="M 171 59v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(-90,178,66)"/><rect x="115" y="73" width="14" height="14" transform="rotate(0,122,80)"/><rect x="129" y="73" width="14" height="14" transform="rotate(0,136,80)"/><rect x="143" y="73" width="14" height="14" transform="rotate(0,150,80)"/><rect x="157" y="73" width="14" height="14" transform="rotate(0,164,80)"/><rect x="171" y="73" width="14" height="14" transform="rotate(0,178,80)"/><path d="M 115 87v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(90,122,94)"/><path d="M 143 87v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(90,150,94)"/><path d="M 171 87v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(90,178,94)"/><circle cx="164" cy="108" r="7" transform="rotate(0,164,108)"/><path d="M 17 115v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(-90,24,122)"/><rect x="31" y="115" width="14" height="14" transform="rotate(0,38,122)"/><rect x="45" y="115" width="14" height="14" transform="rotate(0,52,122)"/><rect x="59" y="115" width="14" height="14" transform="rotate(0,66,122)"/><rect x="73" y="115" width="14" height="14" transform="rotate(0,80,122)"/><rect x="87" y="115" width="14" height="14" transform="rotate(0,94,122)"/><path d="M 101 115v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(0,108,122)"/><path d="M 213 115v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(180,220,122)"/><path d="M 227 115v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(0,234,122)"/><path d="M 283 115v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(-90,290,122)"/><path d="M 3 129v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(180,10,136)"/><rect x="17" y="129" width="14" height="14" transform="rotate(0,24,136)"/><path d="M 31 129v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(90,38,136)"/><rect x="59" y="129" width="14" height="14" transform="rotate(0,66,136)"/><rect x="73" y="129" width="14" height="14" transform="rotate(0,80,136)"/><rect x="101" y="129" width="14" height="14" transform="rotate(0,108,136)"/><path d="M 185 129v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(180,192,136)"/><path d="M 199 129v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(0,206,136)"/><path d="M 269 129v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(-90,276,136)"/><path d="M 283 129v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(90,290,136)"/><rect x="17" y="143" width="14" height="14" transform="rotate(0,24,150)"/><path d="M 59 143v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(180,66,150)"/><rect x="73" y="143" width="14" height="14" transform="rotate(0,80,150)"/><rect x="87" y="143" width="14" height="14" transform="rotate(0,94,150)"/><path d="M 101 143v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(90,108,150)"/><rect x="199" y="143" width="14" height="14" transform="rotate(0,206,150)"/><path d="M 213 143v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(0,220,150)"/><path d="M 269 143v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(90,276,150)"/><path d="M 17 157v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(180,24,164)"/><rect x="31" y="157" width="14" height="14" transform="rotate(0,38,164)"/><path d="M 45 157v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(0,52,164)"/><path d="M 185 157v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(180,192,164)"/><path d="M 199 157v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(90,206,164)"/><path d="M 255 157v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(-90,262,164)"/><circle cx="290" cy="164" r="7" transform="rotate(0,290,164)"/><path d="M 45 171v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(90,52,178)"/><path d="M 87 171v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(180,94,178)"/><path d="M 101 171v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(0,108,178)"/><path d="M 213 171v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(-90,220,178)"/><path d="M 241 171v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(180,248,178)"/><rect x="255" y="171" width="14" height="14" transform="rotate(0,262,178)"/><path d="M 115 185v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(-90,122,192)"/><path d="M 129 185v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(0,136,192)"/><path d="M 157 185v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(-90,164,192)"/><path d="M 213 185v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(90,220,192)"/><path d="M 255 185v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(180,262,192)"/><path d="M 269 185v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(0,276,192)"/><rect x="115" y="199" width="14" height="14" transform="rotate(0,122,206)"/><rect x="129" y="199" width="14" height="14" transform="rotate(0,136,206)"/><path d="M 157 199v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(90,164,206)"/><circle cx="192" cy="206" r="7" transform="rotate(0,192,206)"/><path d="M 227 199v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(-90,234,206)"/><path d="M 269 199v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(90,276,206)"/><rect x="115" y="213" width="14" height="14" transform="rotate(0,122,220)"/><rect x="129" y="213" width="14" height="14" transform="rotate(0,136,220)"/><circle cx="178" cy="220" r="7" transform="rotate(0,178,220)"/><path d="M 213 213v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(180,220,220)"/><rect x="227" y="213" width="14" height="14" transform="rotate(0,234,220)"/><path d="M 255 213v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(-90,262,220)"/><circle cx="290" cy="220" r="7" transform="rotate(0,290,220)"/><rect x="115" y="227" width="14" height="14" transform="rotate(0,122,234)"/><rect x="129" y="227" width="14" height="14" transform="rotate(0,136,234)"/><path d="M 143 227v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(0,150,234)"/><path d="M 185 227v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(-90,192,234)"/><path d="M 227 227v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(90,234,234)"/><path d="M 255 227v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(90,262,234)"/><rect x="115" y="241" width="14" height="14" transform="rotate(0,122,248)"/><rect x="143" y="241" width="14" height="14" transform="rotate(0,150,248)"/><rect x="157" y="241" width="14" height="14" transform="rotate(0,164,248)"/><rect x="171" y="241" width="14" height="14" transform="rotate(0,178,248)"/><rect x="185" y="241" width="14" height="14" transform="rotate(0,192,248)"/><path d="M 199 241v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(0,206,248)"/><circle cx="248" cy="248" r="7" transform="rotate(0,248,248)"/><rect x="115" y="255" width="14" height="14" transform="rotate(0,122,262)"/><rect x="129" y="255" width="14" height="14" transform="rotate(0,136,262)"/><path d="M 143 255v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(90,150,262)"/><rect x="171" y="255" width="14" height="14" transform="rotate(0,178,262)"/><rect x="199" y="255" width="14" height="14" transform="rotate(0,206,262)"/><path d="M 213 255v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(0,220,262)"/><path d="M 115 269v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(180,122,276)"/><path d="M 129 269v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(90,136,276)"/><path d="M 157 269v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(180,164,276)"/><rect x="171" y="269" width="14" height="14" transform="rotate(0,178,276)"/><rect x="185" y="269" width="14" height="14" transform="rotate(0,192,276)"/><path d="M 199 269v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(90,206,276)"/><circle cx="248" cy="276" r="7" transform="rotate(0,248,276)"/><circle cx="290" cy="276" r="7" transform="rotate(0,290,276)"/><circle cx="150" cy="290" r="7" transform="rotate(0,150,290)"/><path d="M 171 283v 14h 7a 7 7, 0, 0, 0, 0 -14" transform="rotate(90,178,290)"/><circle cx="220" cy="290" r="7" transform="rotate(0,220,290)"/><circle cx="262" cy="290" r="7" transform="rotate(0,262,290)"/><path d="M 3 3v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(-90,10,10)"/><rect x="17" y="3" width="14" height="14" transform="rotate(0,24,10)"/><rect x="31" y="3" width="14" height="14" transform="rotate(0,38,10)"/><rect x="45" y="3" width="14" height="14" transform="rotate(0,52,10)"/><rect x="59" y="3" width="14" height="14" transform="rotate(0,66,10)"/><rect x="73" y="3" width="14" height="14" transform="rotate(0,80,10)"/><path d="M 87 3v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(0,94,10)"/><rect x="3" y="17" width="14" height="14" transform="rotate(0,10,24)"/><rect x="87" y="17" width="14" height="14" transform="rotate(0,94,24)"/><rect x="3" y="31" width="14" height="14" transform="rotate(0,10,38)"/><rect x="87" y="31" width="14" height="14" transform="rotate(0,94,38)"/><rect x="3" y="45" width="14" height="14" transform="rotate(0,10,52)"/><rect x="87" y="45" width="14" height="14" transform="rotate(0,94,52)"/><rect x="3" y="59" width="14" height="14" transform="rotate(0,10,66)"/><rect x="87" y="59" width="14" height="14" transform="rotate(0,94,66)"/><rect x="3" y="73" width="14" height="14" transform="rotate(0,10,80)"/><rect x="87" y="73" width="14" height="14" transform="rotate(0,94,80)"/><path d="M 3 87v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(180,10,94)"/><rect x="17" y="87" width="14" height="14" transform="rotate(0,24,94)"/><rect x="31" y="87" width="14" height="14" transform="rotate(0,38,94)"/><rect x="45" y="87" width="14" height="14" transform="rotate(0,52,94)"/><rect x="59" y="87" width="14" height="14" transform="rotate(0,66,94)"/><rect x="73" y="87" width="14" height="14" transform="rotate(0,80,94)"/><path d="M 87 87v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(90,94,94)"/><path d="M 31 31v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(-90,38,38)"/><rect x="45" y="31" width="14" height="14" transform="rotate(0,52,38)"/><path d="M 59 31v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(0,66,38)"/><rect x="31" y="45" width="14" height="14" transform="rotate(0,38,52)"/><rect x="45" y="45" width="14" height="14" transform="rotate(0,52,52)"/><rect x="59" y="45" width="14" height="14" transform="rotate(0,66,52)"/><path d="M 31 59v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(180,38,66)"/><rect x="45" y="59" width="14" height="14" transform="rotate(0,52,66)"/><path d="M 59 59v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(90,66,66)"/><path d="M 199 3v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(-90,206,10)"/><rect x="213" y="3" width="14" height="14" transform="rotate(0,220,10)"/><rect x="227" y="3" width="14" height="14" transform="rotate(0,234,10)"/><rect x="241" y="3" width="14" height="14" transform="rotate(0,248,10)"/><rect x="255" y="3" width="14" height="14" transform="rotate(0,262,10)"/><rect x="269" y="3" width="14" height="14" transform="rotate(0,276,10)"/><path d="M 283 3v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(0,290,10)"/><rect x="199" y="17" width="14" height="14" transform="rotate(0,206,24)"/><rect x="283" y="17" width="14" height="14" transform="rotate(0,290,24)"/><rect x="199" y="31" width="14" height="14" transform="rotate(0,206,38)"/><rect x="283" y="31" width="14" height="14" transform="rotate(0,290,38)"/><rect x="199" y="45" width="14" height="14" transform="rotate(0,206,52)"/><rect x="283" y="45" width="14" height="14" transform="rotate(0,290,52)"/><rect x="199" y="59" width="14" height="14" transform="rotate(0,206,66)"/><rect x="283" y="59" width="14" height="14" transform="rotate(0,290,66)"/><rect x="199" y="73" width="14" height="14" transform="rotate(0,206,80)"/><rect x="283" y="73" width="14" height="14" transform="rotate(0,290,80)"/><path d="M 199 87v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(180,206,94)"/><rect x="213" y="87" width="14" height="14" transform="rotate(0,220,94)"/><rect x="227" y="87" width="14" height="14" transform="rotate(0,234,94)"/><rect x="241" y="87" width="14" height="14" transform="rotate(0,248,94)"/><rect x="255" y="87" width="14" height="14" transform="rotate(0,262,94)"/><rect x="269" y="87" width="14" height="14" transform="rotate(0,276,94)"/><path d="M 283 87v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(90,290,94)"/><path d="M 227 31v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(-90,234,38)"/><rect x="241" y="31" width="14" height="14" transform="rotate(0,248,38)"/><path d="M 255 31v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(0,262,38)"/><rect x="227" y="45" width="14" height="14" transform="rotate(0,234,52)"/><rect x="241" y="45" width="14" height="14" transform="rotate(0,248,52)"/><rect x="255" y="45" width="14" height="14" transform="rotate(0,262,52)"/><path d="M 227 59v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(180,234,66)"/><rect x="241" y="59" width="14" height="14" transform="rotate(0,248,66)"/><path d="M 255 59v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(90,262,66)"/><path d="M 3 199v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(-90,10,206)"/><rect x="17" y="199" width="14" height="14" transform="rotate(0,24,206)"/><rect x="31" y="199" width="14" height="14" transform="rotate(0,38,206)"/><rect x="45" y="199" width="14" height="14" transform="rotate(0,52,206)"/><rect x="59" y="199" width="14" height="14" transform="rotate(0,66,206)"/><rect x="73" y="199" width="14" height="14" transform="rotate(0,80,206)"/><path d="M 87 199v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(0,94,206)"/><rect x="3" y="213" width="14" height="14" transform="rotate(0,10,220)"/><rect x="87" y="213" width="14" height="14" transform="rotate(0,94,220)"/><rect x="3" y="227" width="14" height="14" transform="rotate(0,10,234)"/><rect x="87" y="227" width="14" height="14" transform="rotate(0,94,234)"/><rect x="3" y="241" width="14" height="14" transform="rotate(0,10,248)"/><rect x="87" y="241" width="14" height="14" transform="rotate(0,94,248)"/><rect x="3" y="255" width="14" height="14" transform="rotate(0,10,262)"/><rect x="87" y="255" width="14" height="14" transform="rotate(0,94,262)"/><rect x="3" y="269" width="14" height="14" transform="rotate(0,10,276)"/><rect x="87" y="269" width="14" height="14" transform="rotate(0,94,276)"/><path d="M 3 283v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(180,10,290)"/><rect x="17" y="283" width="14" height="14" transform="rotate(0,24,290)"/><rect x="31" y="283" width="14" height="14" transform="rotate(0,38,290)"/><rect x="45" y="283" width="14" height="14" transform="rotate(0,52,290)"/><rect x="59" y="283" width="14" height="14" transform="rotate(0,66,290)"/><rect x="73" y="283" width="14" height="14" transform="rotate(0,80,290)"/><path d="M 87 283v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(90,94,290)"/><path d="M 31 227v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(-90,38,234)"/><rect x="45" y="227" width="14" height="14" transform="rotate(0,52,234)"/><path d="M 59 227v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(0,66,234)"/><rect x="31" y="241" width="14" height="14" transform="rotate(0,38,248)"/><rect x="45" y="241" width="14" height="14" transform="rotate(0,52,248)"/><rect x="59" y="241" width="14" height="14" transform="rotate(0,66,248)"/><path d="M 31 255v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(180,38,262)"/><rect x="45" y="255" width="14" height="14" transform="rotate(0,52,262)"/><path d="M 59 255v 14h 14v -7a 7 7, 0, 0, 0, -7 -7" transform="rotate(90,66,262)"/></clipPath></defs><rect x="0" y="0" height="300" width="300" clip-path="url('#clip-path-background-color-2')" fill="#e9ebee"/><rect x="0" y="0" height="300" width="300" clip-path="url('#clip-path-dot-color-2')" fill="#4267b2"/><image href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAQAAAAnOwc2AAAAEUlEQVR42mNk+M+AARiHsiAAcCIKAYwFoQ8AAAAASUVORK5CYII=" x="115" y="115" width="70px" height="70px"/></svg>