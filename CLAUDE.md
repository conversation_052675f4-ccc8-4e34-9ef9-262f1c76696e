# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

QR Code Styling is a TypeScript library for generating customizable QR codes with logos and advanced styling options. It supports both SVG and Canvas rendering, works in browsers and Node.js environments, and provides extensive customization through options for dots, corners, gradients, and embedded images.

## Development Commands

- `npm run build` - Production build (webpack with minification)
- `npm run build:dev` - Development build (webpack without minification)
- `npm run test` - Run Jest tests with coverage
- `npm start` - Start development server with hot reload
- `npm run dev` - Start development server (same as start)

## Architecture Overview

### Core Components

- **QRCodeStyling** (`src/core/QRCodeStyling.ts`) - Main class that orchestrates QR code generation
- **QRSVG** (`src/core/QRSVG.ts`) - Handles SVG rendering logic
- **QROptions** (`src/core/QROptions.ts`) - Configuration and default options management

### Key Directories

- `src/core/` - Core QR code generation and rendering logic
- `src/constants/` - Enums and constants for types, modes, error correction levels
- `src/figures/` - Shape rendering classes (dots, corners, backgrounds)
- `src/tools/` - Utility functions (sanitization, merging, mode detection)
- `src/types/` - TypeScript type definitions
- `src/image/` - Image handling and processing
- `src/styles/` - Style and gradient utilities

### Rendering Pipeline

1. **Options Processing**: User options merged with defaults via `sanitizeOptions`
2. **QR Generation**: Uses `qrcode-generator` library to create QR matrix
3. **Rendering**: Chooses SVG or Canvas based on `type` option
4. **Output**: Supports download, blob generation, or DOM appending

### Browser vs Node.js Support

The library supports both environments:
- **Browser**: Uses DOM Canvas and SVG APIs
- **Node.js**: Requires `canvas` and `jsdom` dependencies passed via options

### Build System

- **Webpack**: Multiple configs for different environments
  - `webpack.config.common.js` - Shared configuration
  - `webpack.config.build.js` - Production builds 
  - `webpack.config.dev-server.js` - Development server
- **TypeScript**: Strict mode enabled, outputs to `lib/`
- **ESLint**: Modern flat config with TypeScript and Jest support

### Testing

- **Jest** with `ts-jest` preset
- **jsdom** environment for browser API simulation
- Coverage collection enabled for all TypeScript files
- Test files use `.test.js` or `.test.ts` extensions

## Common Development Patterns

When adding new features:
1. Add type definitions in `src/types/`
2. Update default options in `src/core/QROptions.ts`
3. Implement logic in appropriate core classes
4. Add constants if needed in `src/constants/`
5. Write tests alongside implementation

The codebase follows a modular architecture where each component has a single responsibility, making it easier to extend and maintain.