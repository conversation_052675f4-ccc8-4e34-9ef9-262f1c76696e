// Main exports for the pypix-ts library
export { Pix } from './pix';
export { GeneratorQR } from './core/qrgen';
export { parseBrCode, crcCompute } from './core/utils/pixParser';
export { validateCPF, validatePhone } from './core/utils/validators';
export { getValue, formattedText, base64Image } from './core/utils/services';
export * from './types';

// Re-export styling constants for convenience
export { MARKER_SVGS } from './core/styles/markerStyles';
export { BORDER_SVGS } from './core/styles/borderStyles';
export { FRAME_SVGS } from './core/styles/frameStyles';