import { FrameStyle } from '../../types';

export const FRAME_SVGS: Record<FrameStyle, string> = {
  [FrameStyle.CLEAN]: `<svg xmlns="http://www.w3.org/2000/svg" width="300" height="350" viewBox="0 0 300 350">
    <rect width="300" height="350" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2"/>
    <rect x="25" y="25" width="250" height="250" fill="white"/>
    <text x="150" y="315" text-anchor="middle" fill="#6c757d" font-family="Arial, sans-serif" font-size="14">Scan to Pay</text>
  </svg>`,
  
  [FrameStyle.TECH]: `<svg xmlns="http://www.w3.org/2000/svg" width="320" height="370" viewBox="0 0 320 370">
    <defs>
      <linearGradient id="techGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
      </linearGradient>
    </defs>
    <rect width="320" height="370" fill="url(#techGrad)"/>
    <rect x="35" y="35" width="250" height="250" fill="white"/>
    <text x="160" y="330" text-anchor="middle" fill="white" font-family="monospace" font-size="16" font-weight="bold">TECH_QR.SCAN()</text>
  </svg>`,
  
  [FrameStyle.CREATIVE]: `<svg xmlns="http://www.w3.org/2000/svg" width="320" height="380" viewBox="0 0 320 380">
    <defs>
      <radialGradient id="creativeGrad" cx="50%" cy="50%" r="50%">
        <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#4ecdc4;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:1" />
      </radialGradient>
    </defs>
    <rect width="320" height="380" fill="url(#creativeGrad)" rx="20"/>
    <rect x="35" y="35" width="250" height="250" fill="white" rx="10"/>
    <text x="160" y="330" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">✨ Scan Me ✨</text>
  </svg>`,
  
  [FrameStyle.PAY]: `<svg xmlns="http://www.w3.org/2000/svg" width="300" height="380" viewBox="0 0 300 380">
    <rect width="300" height="380" fill="#2d3748"/>
    <rect x="25" y="35" width="250" height="250" fill="white"/>
    <text x="150" y="320" text-anchor="middle" fill="#48bb78" font-family="Arial, sans-serif" font-size="16" font-weight="bold">PIX Payment</text>
    <text x="150" y="345" text-anchor="middle" fill="#a0aec0" font-family="Arial, sans-serif" font-size="12">Scan to complete payment</text>
  </svg>`,
  
  [FrameStyle.SCAN_ME_PURPLE]: `<svg xmlns="http://www.w3.org/2000/svg" width="320" height="400" viewBox="0 0 320 400">
    <defs>
      <linearGradient id="purpleGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
      </linearGradient>
    </defs>
    <rect width="320" height="400" fill="url(#purpleGrad)" rx="15"/>
    <rect x="35" y="35" width="250" height="250" fill="white" rx="10"/>
    <rect x="35" y="300" width="250" height="60" fill="rgba(255,255,255,0.1)" rx="10"/>
    <text x="160" y="325" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">SCAN ME</text>
    <text x="160" y="345" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="14">to make payment</text>
  </svg>`,
  
  [FrameStyle.SCAN_ME_NEON]: `<svg xmlns="http://www.w3.org/2000/svg" width="320" height="400" viewBox="0 0 320 400">
    <defs>
      <linearGradient id="neonGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#00f5ff;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#ff00ff;stop-opacity:1" />
      </linearGradient>
    </defs>
    <rect width="320" height="400" fill="#000" rx="15"/>
    <rect x="8" y="8" width="304" height="384" fill="none" stroke="url(#neonGrad)" stroke-width="4" rx="15"/>
    <rect x="35" y="35" width="250" height="250" fill="white" rx="10"/>
    <text x="160" y="330" text-anchor="middle" fill="#00f5ff" font-family="monospace" font-size="18" font-weight="bold">⚡ SCAN ME ⚡</text>
    <text x="160" y="355" text-anchor="middle" fill="#ff00ff" font-family="monospace" font-size="12">NEON_PAYMENT_v2.0</text>
  </svg>`,
  
  [FrameStyle.SCAN_ME_TECH]: `<svg xmlns="http://www.w3.org/2000/svg" width="340" height="400" viewBox="0 0 340 400">
    <rect width="340" height="400" fill="#1a202c"/>
    <rect x="20" y="20" width="300" height="300" fill="white"/>
    <rect x="20" y="340" width="300" height="40" fill="#2d3748"/>
    <text x="170" y="365" text-anchor="middle" fill="#4fd1c7" font-family="monospace" font-size="16" font-weight="bold">[SCAN_TO_PAY]</text>
    <!-- Corner decorations -->
    <polygon points="20,20 40,20 20,40" fill="#4fd1c7"/>
    <polygon points="300,20 320,20 320,40" fill="#4fd1c7"/>
    <polygon points="20,300 20,320 40,320" fill="#4fd1c7"/>
    <polygon points="300,320 320,320 320,300" fill="#4fd1c7"/>
  </svg>`
};