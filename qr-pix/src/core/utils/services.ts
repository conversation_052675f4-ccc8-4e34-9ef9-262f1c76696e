/**
 * Formats value according to TLV (Tag-Length-Value) format
 * @param identify Identifier of the value type (e.g., '00', '01', etc.)
 * @param value Value to be formatted
 * @returns Formatted value as string with identifier and value length
 */
export function getValue(identify: string, value: string): string {
  return `${identify}${value.length.toString().padStart(2, '0')}${value}`;
}

/**
 * Formats text by removing special characters and normalizing
 * @param value Text to be formatted
 * @returns Formatted text containing only alphanumeric characters and some allowed symbols
 */
export function formattedText(value: string): string {
  // Normalize unicode characters and remove accents
  const normalized = value.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
  
  // Keep only allowed characters: A-Z, a-z, 0-9, $, @, %, *, +, -, ., /, :, _, space
  return normalized.replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '');
}

/**
 * Converts image to base64 string
 * @param buffer Image buffer
 * @param mimeType MIME type of the image
 * @returns Base64 encoded string
 */
export function base64Image(buffer: Buffer, mimeType: string = 'image/png'): string {
  const base64Data = buffer.toString('base64');
  return `data:${mimeType};base64,${base64Data}`;
}