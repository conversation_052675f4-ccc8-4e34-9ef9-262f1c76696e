import { ParsedBRCode } from '../../types';

/**
 * Calculates CRC16-CCITT-FALSE for PIX data string
 * @param data Data string for CRC calculation
 * @returns CRC16 checksum as 4-character uppercase hex string
 */
export function crcCompute(data: string): string {
  let crc = 0xFFFF;
  const dataBytes = Buffer.from(data, 'utf-8');

  for (const byte of dataBytes) {
    crc ^= (byte << 8);
    for (let i = 0; i < 8; i++) {
      if (crc & 0x8000) {
        crc = (crc << 1) ^ 0x1021;
      } else {
        crc <<= 1;
      }
    }
  }

  return (crc & 0xFFFF).toString(16).toUpperCase().padStart(4, '0');
}

/**
 * Parses a TLV (Tag-Length-Value) format string and returns it as a dictionary
 * This function is recursive to handle nested fields
 * @param dataString TLV format string
 * @returns Parsed data as object
 */
function parseTlv(dataString: string): Record<string, any> {
  const data: Record<string, any> = {};
  let i = 0;

  while (i < dataString.length) {
    const tag = dataString.slice(i, i + 2);
    
    if (dataString.slice(i + 2, i + 4).length !== 2) {
      throw new Error(`Invalid TLV format near tag ${tag}. Malformed length.`);
    }

    const length = parseInt(dataString.slice(i + 2, i + 4), 10);
    const value = dataString.slice(i + 4, i + 4 + length);

    // If it's a field that can contain other TLVs, call the function recursively
    if (['26', '62'].includes(tag)) {
      data[tag] = parseTlv(value);
    } else {
      data[tag] = value;
    }

    i += 4 + length;
  }

  return data;
}

/**
 * Main function to parse the complete PIX BR Code string
 * Separates payload from CRC, validates checksum and formats output into readable dictionary
 * @param brCodeString Complete BR Code string
 * @returns Parsed PIX data
 */
export function parseBrCode(brCodeString: string): ParsedBRCode {
  const crcFieldIndex = brCodeString.lastIndexOf('6304');
  
  if (crcFieldIndex === -1 || brCodeString.slice(crcFieldIndex).length !== 8) {
    throw new Error('Invalid PIX string: CRC field (ID 63) not found or malformed.');
  }

  const payloadForCrcCalc = brCodeString.slice(0, -4);
  const payloadForParsing = brCodeString.slice(0, crcFieldIndex); // Parse only until before CRC field
  const providedCrc = brCodeString.slice(-4);

  const calculatedCrc = crcCompute(payloadForCrcCalc);
  const isCrcValid = (calculatedCrc === providedCrc);

  if (!isCrcValid) {
    throw new Error(`CRC validation failed. Expected: ${calculatedCrc}, Got: ${providedCrc}`);
  }

  const parsedData = parseTlv(payloadForParsing);
  const merchantInfo = parsedData['26'] || {};
  const additionalData = parsedData['62'] || {};

  return {
    nome: parsedData['59'],
    cidade: parsedData['60'],
    valor: parsedData['54'] ? parseFloat(parsedData['54']) : 0,
    chave: merchantInfo['01'],
    txid: additionalData['05'] || '***',
    cep: parsedData['61'],
    descricao: merchantInfo['02'],
    // Include raw parsed data for debugging
    _raw: {
      dados_gerais: {
        payload_format_indicator: parsedData['00'],
        point_of_initiation_method: parsedData['01'] === '11' ? 'Estático' : 'Dinâmico',
        merchant_category_code: parsedData['52'],
        transaction_currency: parsedData['53'],
        country_code: parsedData['58'],
      },
      merchant_info: merchantInfo,
      additional_data: additionalData,
      crc_valid: isCrcValid
    }
  };
}