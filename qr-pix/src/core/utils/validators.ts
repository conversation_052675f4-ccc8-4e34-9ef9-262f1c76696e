/**
 * Validates Brazilian CPF (Cadastro de Pessoas Físicas)
 * @param numbers CPF number as string, may include non-numeric characters
 * @returns true if CPF is valid, false otherwise
 */
export function validateCPF(numbers: string): boolean {
  const cpf = numbers.replace(/\D/g, '').split('').map(Number);
  
  if (cpf.length !== 11) {
    return false;
  }
  
  // Check if all digits are the same
  if (cpf.every(digit => digit === cpf[0])) {
    return false;
  }
  
  // Validate check digits
  for (let i = 9; i < 11; i++) {
    const value = cpf.slice(0, i).reduce((sum, digit, index) => {
      return sum + digit * ((i + 1) - index);
    }, 0);
    const digit = ((value * 10) % 11) % 10;
    if (digit !== cpf[i]) {
      return false;
    }
  }
  
  return true;
}

/**
 * Validates Brazilian phone number
 * @param value Phone number as string, may include non-numeric characters
 * @returns true if phone is valid, false otherwise
 */
export function validatePhone(value: string): boolean {
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return phoneRegex.test(value);
}