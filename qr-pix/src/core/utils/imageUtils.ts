import * as fs from 'fs';
import * as path from 'path';
import { createCanvas, loadImage, Canvas, CanvasRenderingContext2D } from 'canvas';
import sharp from 'sharp';
import Jimp from 'jimp';

/**
 * Converts SVG string to PNG buffer using Sharp
 * @param svgString SVG content as string
 * @param width Target width
 * @param height Target height
 * @param color Fill color for the SVG
 * @returns PNG buffer
 */
export async function svgToPng(
  svgString: string, 
  width: number = 100, 
  height: number = 100,
  color: string = '#000000'
): Promise<Buffer> {
  // Add fill color to SVG if not present
  const coloredSvg = svgString.includes('fill=') 
    ? svgString 
    : svgString.replace(/(<path|<rect|<circle|<polygon)([^>]*>)/g, `$1$2`.replace('>', ` fill="${color}">`));
  
  return await sharp(Buffer.from(coloredSvg))
    .resize(width, height)
    .png()
    .toBuffer();
}

/**
 * Adds a center image (logo) to a QR code
 * @param qrBuffer QR code image buffer
 * @param logoPath Path to the logo image
 * @param logoSizePercent Logo size as percentage of QR code (0.0-1.0)
 * @returns Modified QR code buffer
 */
export async function addCenterImage(
  qrBuffer: Buffer,
  logoPath: string,
  logoSizePercent: number = 0.25
): Promise<Buffer> {
  if (!fs.existsSync(logoPath)) {
    throw new Error(`Logo file not found: ${logoPath}`);
  }

  const qrImage = await sharp(qrBuffer);
  const { width, height } = await qrImage.metadata();
  
  if (!width || !height) {
    throw new Error('Invalid QR code image dimensions');
  }

  const logoSize = Math.min(width, height) * logoSizePercent;
  
  const logo = await sharp(logoPath)
    .resize(Math.round(logoSize), Math.round(logoSize), {
      fit: 'contain',
      background: { r: 255, g: 255, b: 255, alpha: 1 }
    })
    .png()
    .toBuffer();

  return await qrImage
    .composite([{
      input: logo,
      top: Math.round((height - logoSize) / 2),
      left: Math.round((width - logoSize) / 2)
    }])
    .png()
    .toBuffer();
}

/**
 * Creates animated GIF frames by adding different GIF frames to QR code center
 * @param qrBuffer QR code image buffer
 * @param gifPath Path to the GIF file
 * @param gifSizePercent GIF size as percentage of QR code
 * @returns Array of frame buffers and frame duration
 */
export async function addCenterGif(
  qrBuffer: Buffer,
  gifPath: string,
  gifSizePercent: number = 0.25
): Promise<{ frames: Buffer[], duration: number }> {
  if (!fs.existsSync(gifPath)) {
    throw new Error(`GIF file not found: ${gifPath}`);
  }

  const qrImage = await sharp(qrBuffer);
  const { width, height } = await qrImage.metadata();
  
  if (!width || !height) {
    throw new Error('Invalid QR code image dimensions');
  }

  const gifSize = Math.min(width, height) * gifSizePercent;
  
  // Use Jimp to extract GIF frames
  const gif = await Jimp.read(gifPath);
  const frames: Buffer[] = [];
  let frameIndex = 0;
  
  try {
    while (true) {
      // Convert current frame to buffer
      const frameBuffer = await gif.getBufferAsync(Jimp.MIME_PNG);
      
      // Resize frame and composite with QR code
      const resizedFrame = await sharp(frameBuffer)
        .resize(Math.round(gifSize), Math.round(gifSize), {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        })
        .png()
        .toBuffer();
      
      const compositeFrame = await sharp(qrBuffer)
        .composite([{
          input: resizedFrame,
          top: Math.round((height - gifSize) / 2),
          left: Math.round((width - gifSize) / 2)
        }])
        .png()
        .toBuffer();
      
      frames.push(compositeFrame);
      
      // Move to next frame
      frameIndex++;
      if (frameIndex >= (gif as any).bitmap?.frames?.length || frameIndex > 50) break;
      
      // This is a simplified approach - in practice you'd need a proper GIF decoder
      break;
    }
  } catch (error) {
    // If GIF processing fails, fallback to static image
    console.warn('GIF processing failed, using as static image:', error);
    const staticFrame = await addCenterImage(qrBuffer, gifPath, gifSizePercent);
    frames.push(staticFrame);
  }
  
  return {
    frames: frames.length > 0 ? frames : [qrBuffer],
    duration: 100 // Default 100ms per frame
  };
}

/**
 * Applies a frame around the QR code using SVG template
 * @param qrBuffer QR code image buffer
 * @param frameSvg Frame SVG template
 * @returns QR code with frame applied
 */
export async function applyFrameQr(
  qrBuffer: Buffer,
  frameSvg: string
): Promise<Buffer> {
  // Parse frame SVG to get dimensions
  const widthMatch = frameSvg.match(/width="(\d+)"/);
  const heightMatch = frameSvg.match(/height="(\d+)"/);
  
  const frameWidth = widthMatch ? parseInt(widthMatch[1]) : 300;
  const frameHeight = heightMatch ? parseInt(heightMatch[1]) : 350;
  
  // Convert frame SVG to PNG
  const frameBuffer = await svgToPng(frameSvg, frameWidth, frameHeight);
  
  // Get QR code dimensions
  const qrMeta = await sharp(qrBuffer).metadata();
  const qrSize = 250; // Standard QR size for frames
  
  // Resize QR code to fit in frame
  const resizedQr = await sharp(qrBuffer)
    .resize(qrSize, qrSize)
    .png()
    .toBuffer();
  
  // Calculate position to center QR code in frame (usually around 25,35 offset)
  const qrLeft = Math.round((frameWidth - qrSize) / 2);
  const qrTop = 35; // Standard top offset for frames
  
  // Composite QR code over frame
  return await sharp(frameBuffer)
    .composite([{
      input: resizedQr,
      top: qrTop,
      left: qrLeft
    }])
    .png()
    .toBuffer();
}

/**
 * Saves buffer as image file
 * @param buffer Image buffer
 * @param outputPath Output file path
 * @returns Promise resolving to file path
 */
export async function saveImage(buffer: Buffer, outputPath: string): Promise<string> {
  await fs.promises.writeFile(outputPath, buffer);
  return outputPath;
}

/**
 * Saves multiple frames as animated GIF
 * @param frames Array of image buffers
 * @param outputPath Output GIF file path
 * @param duration Frame duration in milliseconds
 * @returns Promise resolving to file path
 */
export async function saveGif(
  frames: Buffer[],
  outputPath: string,
  duration: number = 100
): Promise<string> {
  if (frames.length === 1) {
    // Save as static PNG if only one frame
    const pngPath = outputPath.replace(/\.gif$/i, '.png');
    await saveImage(frames[0], pngPath);
    return pngPath;
  }
  
  try {
    // Use Sharp to create animated GIF
    await sharp(frames[0], { animated: true })
      .gif({ 
        delay: duration,
        loop: 0 // Infinite loop
      })
      .toFile(outputPath);
    
    return outputPath;
  } catch (error) {
    console.warn('GIF creation failed, saving first frame as PNG:', error);
    const pngPath = outputPath.replace(/\.gif$/i, '.png');
    await saveImage(frames[0], pngPath);
    return pngPath;
  }
}