import QRCode from 'qrcode';
import { createCanvas } from 'canvas';
import sharp from 'sharp';
import { MarkerStyle, BorderStyle, LineStyle, FrameStyle, GradientMode } from '../types';
import { MARKER_SVGS } from './styles/markerStyles';
import { BORDER_SVGS } from './styles/borderStyles';
import { FRAME_SVGS } from './styles/frameStyles';
import { 
  svgToPng, 
  addCenterImage, 
  addCenterGif, 
  applyFrameQr, 
  saveImage, 
  saveGif 
} from './utils/imageUtils';
import { base64Image } from './utils/services';

export interface CustomQROptions {
  data: string;
  size?: number;
  border?: number;
  centerImage?: string;
  markerStyle?: MarkerStyle;
  borderStyle?: BorderStyle;
  lineStyle?: LineStyle;
  gradientColor?: string;
  gradientMode?: GradientMode;
  frameStyle?: FrameStyle;
  styleMode?: 'Normal' | 'Full';
}

/**
 * Custom QR code generator with advanced styling capabilities
 */
export class GeneratorQR {
  /**
   * Creates a custom styled QR code
   * @param options QR code generation options
   * @returns Promise resolving to image buffer
   */
  async createCustomQr(options: CustomQROptions): Promise<Buffer> {
    const {
      data,
      size = 10,
      border = 4,
      centerImage,
      markerStyle,
      borderStyle,
      lineStyle,
      gradientColor,
      gradientMode,
      frameStyle,
      styleMode = 'Normal'
    } = options;

    // Generate base QR code using qrcode library
    let qrBuffer = await this.generateBaseQRCode(data, size, border, lineStyle);

    // Apply styling based on styleMode
    if (styleMode === 'Full') {
      // Apply gradient if specified
      if (gradientColor && gradientMode) {
        qrBuffer = await this.applyGradient(qrBuffer, gradientColor, gradientMode);
      }

      // Apply custom markers
      if (markerStyle || borderStyle) {
        qrBuffer = await this.applyCustomMarkers(qrBuffer, markerStyle, borderStyle);
      }
    }

    // Add center image if specified
    if (centerImage && !centerImage.endsWith('.gif')) {
      qrBuffer = await addCenterImage(qrBuffer, centerImage, 0.25);
    }

    return qrBuffer;
  }

  /**
   * Generates base QR code using the qrcode library
   * @param data QR code data
   * @param size Module size
   * @param border Border size
   * @param lineStyle Line style (affects module drawer)
   * @returns Promise resolving to QR code buffer
   */
  private async generateBaseQRCode(
    data: string, 
    size: number, 
    border: number,
    lineStyle?: LineStyle
  ): Promise<Buffer> {
    const options: QRCode.QRCodeToBufferOptions = {
      type: 'png',
      margin: border,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      width: size * 25, // Approximate module size conversion
      errorCorrectionLevel: 'M'
    };

    return await QRCode.toBuffer(data, options);
  }

  /**
   * Applies gradient effect to QR code
   * @param qrBuffer Original QR code buffer
   * @param color Gradient color
   * @param mode Gradient mode
   * @returns Modified QR code buffer
   */
  private async applyGradient(
    qrBuffer: Buffer, 
    color: string, 
    mode: GradientMode
  ): Promise<Buffer> {
    const image = sharp(qrBuffer);
    const { width, height } = await image.metadata();
    
    if (!width || !height) {
      return qrBuffer;
    }

    // Create gradient overlay
    let gradientSvg: string;
    
    switch (mode) {
      case GradientMode.GRADIENT:
        gradientSvg = `
          <svg width="${width}" height="${height}">
            <defs>
              <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
                <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
              </linearGradient>
            </defs>
            <rect width="${width}" height="${height}" fill="url(#grad1)" />
          </svg>
        `;
        break;
      
      case GradientMode.MULTI:
        gradientSvg = `
          <svg width="${width}" height="${height}">
            <defs>
              <radialGradient id="multiGrad" cx="50%" cy="50%" r="50%">
                <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
                <stop offset="50%" style="stop-color:#ff6b6b;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#4ecdc4;stop-opacity:1" />
              </radialGradient>
            </defs>
            <rect width="${width}" height="${height}" fill="url(#multiGrad)" />
          </svg>
        `;
        break;
      
      default:
        return qrBuffer; // No gradient for NORMAL mode
    }

    const gradientBuffer = await svgToPng(gradientSvg, width, height);
    
    // Apply gradient using multiply blend mode
    return await image
      .composite([{
        input: gradientBuffer,
        blend: 'multiply'
      }])
      .png()
      .toBuffer();
  }

  /**
   * Applies custom marker styles to QR code corners
   * @param qrBuffer Original QR code buffer
   * @param markerStyle Marker style
   * @param borderStyle Border style  
   * @returns Modified QR code buffer
   */
  private async applyCustomMarkers(
    qrBuffer: Buffer,
    markerStyle?: MarkerStyle,
    borderStyle?: BorderStyle
  ): Promise<Buffer> {
    if (!markerStyle && !borderStyle) {
      return qrBuffer;
    }

    const image = sharp(qrBuffer);
    const { width, height } = await image.metadata();
    
    if (!width || !height) {
      return qrBuffer;
    }

    // This is a simplified implementation
    // In practice, you'd need more sophisticated QR code structure analysis
    // to properly replace position patterns
    
    const markerSize = Math.floor(width / 10); // Approximate marker size
    const composites: any[] = [];

    if (markerStyle) {
      const markerSvg = MARKER_SVGS[markerStyle];
      const markerBuffer = await svgToPng(markerSvg, markerSize, markerSize);
      
      // Add markers at the three corners (top-left, top-right, bottom-left)
      composites.push(
        { input: markerBuffer, top: 0, left: 0 },
        { input: markerBuffer, top: 0, left: width - markerSize },
        { input: markerBuffer, top: height - markerSize, left: 0 }
      );
    }

    if (borderStyle && composites.length === 0) {
      const borderSvg = BORDER_SVGS[borderStyle];
      const borderBuffer = await svgToPng(borderSvg, markerSize, markerSize);
      
      composites.push(
        { input: borderBuffer, top: 0, left: 0 },
        { input: borderBuffer, top: 0, left: width - markerSize },
        { input: borderBuffer, top: height - markerSize, left: 0 }
      );
    }

    if (composites.length > 0) {
      return await image
        .composite(composites)
        .png()
        .toBuffer();
    }

    return qrBuffer;
  }

  /**
   * Adds animated GIF to center of QR code and saves as GIF
   * @param qrBuffer QR code buffer
   * @param gifPath Path to GIF file
   * @param outputPath Output file path
   * @returns Promise resolving to base64 encoded result
   */
  async addCenterAnimation(
    qrBuffer: Buffer,
    gifPath: string,
    outputPath: string
  ): Promise<string> {
    const { frames, duration } = await addCenterGif(qrBuffer, gifPath, 0.25);
    
    // Ensure output has .gif extension
    const gifOutputPath = outputPath.replace(/\.[^.]+$/, '.gif');
    
    await saveGif(frames, gifOutputPath, duration);
    
    // Return base64 of first frame for preview
    return base64Image(frames[0], 'image/png');
  }

  /**
   * Saves QR code image to file and returns base64
   * @param buffer Image buffer
   * @param outputPath Output file path
   * @returns Promise resolving to base64 encoded image
   */
  async saveImage(buffer: Buffer, outputPath: string): Promise<string> {
    await saveImage(buffer, outputPath);
    return base64Image(buffer, 'image/png');
  }

  /**
   * Applies frame styling to QR code
   * @param qrBuffer QR code buffer
   * @param frameStyle Frame style to apply
   * @returns Promise resolving to framed QR code buffer
   */
  async applyFrame(qrBuffer: Buffer, frameStyle: FrameStyle): Promise<Buffer> {
    const frameSvg = FRAME_SVGS[frameStyle];
    return await applyFrameQr(qrBuffer, frameSvg);
  }
}