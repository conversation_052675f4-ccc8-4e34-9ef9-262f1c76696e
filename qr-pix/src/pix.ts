import { PixConfig, QRCodeOptions } from './types';
import { validateCPF, validatePhone } from './core/utils/validators';
import { getValue, formattedText } from './core/utils/services';
import { crcCompute } from './core/utils/pixParser';
import { GeneratorQR } from './core/qrgen';

/**
 * Main PIX class for generating BR-Code and QR codes
 */
export class Pix {
  private singleTransaction: boolean = false;
  private key?: string;
  private nameReceiver?: string;
  private cityReceiver?: string;
  private amount: number = 0.0;
  private zipcodeReceiver?: string;
  private identification?: string;
  private description?: string;
  private defaultUrlPix?: string;
  private qr: GeneratorQR;

  constructor(config?: PixConfig) {
    this.qr = new GeneratorQR();
    
    if (config) {
      this.configure(config);
    }
  }

  /**
   * Configure PIX instance with provided config
   */
  configure(config: PixConfig): void {
    if (config.key) this.setKey(config.key);
    if (config.nameReceiver) this.setNameReceiver(config.nameReceiver);
    if (config.cityReceiver) this.setCityReceiver(config.cityReceiver);
    if (config.amount !== undefined) this.setAmount(config.amount);
    if (config.zipcodeReceiver) this.setZipcodeReceiver(config.zipcodeReceiver);
    if (config.identification) this.setIdentification(config.identification);
    if (config.description) this.setDescription(config.description);
    if (config.defaultUrlPix) this.setDefaultUrlPix(config.defaultUrlPix);
    if (config.singleTransaction !== undefined) this.isSingleTransaction(config.singleTransaction);
  }

  /**
   * Set default URL for PIX (for dynamic PIX)
   */
  setDefaultUrlPix(defaultUrlPix?: string): void {
    this.defaultUrlPix = defaultUrlPix?.replace('https://', '') || undefined;
  }

  /**
   * Set PIX key (can be CPF, phone, email, or random key)
   */
  setKey(key?: string): void {
    this.key = key;
  }

  /**
   * Set receiver's postal code
   */
  setZipcodeReceiver(zipcode?: string): void {
    this.zipcodeReceiver = zipcode;
  }

  /**
   * Set receiver's name (max 25 characters)
   */
  setNameReceiver(name?: string): void {
    if (name && name.length > 25) {
      throw new Error('The maximum number of characters for the receiver name is 25.');
    }
    this.nameReceiver = name;
  }

  /**
   * Set transaction identification
   */
  setIdentification(identification?: string): void {
    this.identification = identification;
  }

  /**
   * Set transaction description
   */
  setDescription(description?: string): void {
    this.description = description;
  }

  /**
   * Set receiver's city (max 15 characters)
   */
  setCityReceiver(city?: string): void {
    if (city && city.length > 15) {
      throw new Error('The maximum number of characters for the receiver city is 15.');
    }
    this.cityReceiver = city;
  }

  /**
   * Set transaction amount (max 13 characters including decimals)
   */
  setAmount(value?: number): void {
    if (value !== undefined) {
      const formattedValue = value.toFixed(2);
      if (formattedValue.length > 13) {
        throw new Error('The maximum number of characters for the value is 13.');
      }
      this.amount = value;
    }
  }

  /**
   * Set if transaction is single use (for dynamic PIX)
   */
  isSingleTransaction(singleTransaction?: boolean): void {
    this.singleTransaction = singleTransaction || false;
  }

  /**
   * Generate BR-Code string for PIX transaction
   */
  getBrCode(): string {
    const amountStr = this.amount.toFixed(2);
    
    const resultString = [
      getValue('00', '01'), // Payload Format Indicator
      getValue('01', this.singleTransaction ? '12' : '11'), // Point of Initiation Method
      this.getAccountInformation(),
      getValue('52', '0000'), // Merchant Category Code
      getValue('53', '986'), // Transaction Currency (BRL)
      this.key ? getValue('54', amountStr) : '', // Transaction Amount
      getValue('58', 'BR'), // Country Code
      getValue('59', formattedText(this.nameReceiver || '')), // Merchant Name
      getValue('60', formattedText(this.cityReceiver || '')), // Merchant City
      this.zipcodeReceiver ? getValue('61', formattedText(this.zipcodeReceiver)) : '', // Postal Code
      this.getAdditionalDataField(),
      '6304' // CRC placeholder
    ].join('');

    return resultString + crcCompute(resultString);
  }

  /**
   * Generate account information field (field 26)
   */
  private getAccountInformation(): string {
    const basePix = getValue('00', 'br.gov.bcb.pix');
    let infoString = '';

    if (this.key) {
      let processedKey = this.key;
      
      // Handle CPF
      if (processedKey.length === 11 && validateCPF(processedKey)) {
        // Key is valid CPF, use as is
      }
      // Handle phone number
      else if (validatePhone(processedKey)) {
        processedKey = processedKey.startsWith('+55') ? processedKey : `+55${processedKey}`;
      }
      
      infoString += getValue('01', processedKey);
    } else if (this.defaultUrlPix) {
      infoString += getValue('25', this.defaultUrlPix);
    } else {
      throw new Error('You must enter a URL or a PIX key.');
    }

    if (this.description) {
      infoString += getValue('02', formattedText(this.description));
    }

    return getValue('26', basePix + infoString);
  }

  /**
   * Generate additional data field (field 62)
   */
  private getAdditionalDataField(): string {
    const txid = this.identification ? formattedText(this.identification) : '***';
    return getValue('62', getValue('05', txid));
  }

  /**
   * Generate and save QR code with styling options
   */
  async saveQrcode(options: QRCodeOptions = {}): Promise<string | null> {
    try {
      const data = options.data || this.getBrCode();
      const output = options.output || './qrcode.png';
      const isGifLogo = options.customLogo?.endsWith('.gif');

      const qrImg = await this.qr.createCustomQr({
        data,
        size: options.boxSize || 7,
        border: options.border || 1,
        centerImage: !isGifLogo ? options.customLogo : undefined,
        markerStyle: options.markerStyle,
        borderStyle: options.borderStyle,
        lineStyle: options.lineStyle,
        gradientColor: options.gradientColor,
        gradientMode: options.gradientMode,
        frameStyle: options.frameStyle,
        styleMode: options.styleMode || 'Normal'
      });

      // Handle GIF animations if needed
      if (isGifLogo && options.customLogo) {
        return await this.qr.addCenterAnimation(qrImg, options.customLogo, output);
      }

      // Save regular image
      return await this.qr.saveImage(qrImg, output);
    } catch (error) {
      console.error('Error saving QR code:', error);
      return null;
    }
  }
}