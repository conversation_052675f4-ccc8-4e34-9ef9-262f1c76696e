export interface PixConfig {
  key?: string;
  nameReceiver?: string;
  cityReceiver?: string;
  amount?: number;
  zipcodeReceiver?: string;
  identification?: string;
  description?: string;
  defaultUrlPix?: string;
  singleTransaction?: boolean;
}

export interface QRCodeOptions {
  data?: string;
  output?: string;
  boxSize?: number;
  border?: number;
  customLogo?: string;
  markerStyle?: MarkerStyle;
  borderStyle?: BorderStyle;
  lineStyle?: LineStyle;
  gradientColor?: string;
  gradientMode?: GradientMode;
  frameStyle?: FrameStyle;
  styleMode?: 'Normal' | 'Full';
}

export enum MarkerStyle {
  SQUARE = 'square',
  CIRCLE = 'circle',
  ROUNDED = 'rounded',
  QUARTER_CIRCLE = 'quarter_circle',
  STAR = 'star',
  DIAMOND = 'diamond',
  PLUS = 'plus'
}

export enum BorderStyle {
  SQUARE = 'square',
  ROUNDED = 'rounded',
  CIRCLE = 'circle'
}

export enum LineStyle {
  SQUARE = 'square',
  GAPPED_SQUARE = 'gapped_square',
  CIRCLE = 'circle',
  ROUNDED = 'rounded',
  VERTICAL_BARS = 'vertical_bars',
  HORIZONTAL_BARS = 'horizontal_bars'
}

export enum FrameStyle {
  CLEAN = 'clean',
  TECH = 'tech',
  CREATIVE = 'creative',
  PAY = 'pay',
  SCAN_ME_PURPLE = 'scan_me_purple',
  SCAN_ME_NEON = 'scan_me_neon',
  SCAN_ME_TECH = 'scan_me_tech'
}

export enum GradientMode {
  NORMAL = 'normal',
  GRADIENT = 'gradient',
  MULTI = 'multi'
}

export interface ParsedBRCode {
  nome?: string;
  cidade?: string;
  valor?: number;
  chave?: string;
  txid?: string;
  cep?: string;
  descricao?: string;
  [key: string]: any;
}