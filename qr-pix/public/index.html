<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerador PIX QR Code</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Formulário -->
        <div class="form-container">
            <div class="form-header">
                <h1>Gerador PIX QR Code</h1>
                <p>Preencha os dados para gerar seu QR Code PIX</p>
            </div>

            <form id="pixForm" class="pix-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="keyType">Tipo de Chave *</label>
                        <select id="keyType" name="keyType" required>
                            <option value="cpf">CPF</option>
                            <option value="phone">Telefone</option>
                            <option value="email">Email</option>
                            <option value="random">Chave Aleatória</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="pixKey">Chave PIX *</label>
                        <input 
                            type="text" 
                            id="pixKey" 
                            name="pixKey" 
                            placeholder="000.000.000-00"
                            required
                        >
                        <div class="validation-message" id="keyValidation"></div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="receiverName">Nome</label>
                        <input 
                            type="text" 
                            id="receiverName" 
                            name="receiverName"
                            maxlength="25"
                            placeholder="Nome do recebedor"
                            required
                        >
                        <small class="char-counter">0/25 caracteres</small>
                    </div>

                    <div class="form-group">
                        <label for="receiverCity">Cidade</label>
                        <input 
                            type="text" 
                            id="receiverCity" 
                            name="receiverCity"
                            maxlength="15"
                            placeholder="Cidade do recebedor"
                            required
                        >
                        <small class="char-counter">0/15 caracteres</small>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="amount">Valor (opcional)</label>
                        <input 
                            type="text" 
                            id="amount" 
                            name="amount"
                            placeholder="R$ 0,00"
                            class="currency-input"
                        >
                        <small>Deixe em branco para valor livre</small>
                    </div>

                    <div class="form-group">
                        <label for="reference">Referência (opcional)</label>
                        <input 
                            type="text" 
                            id="reference" 
                            name="reference"
                            placeholder="Ex: COMPRA123"
                            maxlength="25"
                        >
                        <small>Use apenas letras e números, sem espaços ou caracteres especiais.</small>
                    </div>
                </div>

                <div class="form-group full-width">
                    <label for="description">Descrição (opcional)</label>
                    <input 
                        type="text" 
                        id="description" 
                        name="description"
                        placeholder="Descrição da transação"
                        maxlength="50"
                    >
                </div>

                <!-- Opções de Estilo -->
                <div class="style-section">
                    <h3>Personalização do QR Code</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="markerStyle">Estilo dos Marcadores</label>
                            <select id="markerStyle" name="markerStyle">
                                <option value="square">Quadrado</option>
                                <option value="circle">Círculo</option>
                                <option value="rounded">Arredondado</option>
                                <option value="quarter_circle">Quarto de Círculo</option>
                                <option value="star">Estrela</option>
                                <option value="diamond">Diamante</option>
                                <option value="plus">Cruz</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="frameStyle">Estilo da Moldura</label>
                            <select id="frameStyle" name="frameStyle">
                                <option value="">Sem moldura</option>
                                <option value="clean">Limpo</option>
                                <option value="tech">Tecnológico</option>
                                <option value="creative">Criativo</option>
                                <option value="scan_me_purple">Scan Me Roxo</option>
                                <option value="scan_me_neon">Scan Me Neon</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="gradientColor">Cor do Gradiente</label>
                            <select id="gradientColor" name="gradientColor">
                                <option value="blue">Azul</option>
                                <option value="purple">Roxo</option>
                                <option value="green">Verde</option>
                                <option value="red">Vermelho</option>
                                <option value="orange">Laranja</option>
                                <option value="pink">Rosa</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="gradientMode">Tipo de Gradiente</label>
                            <select id="gradientMode" name="gradientMode">
                                <option value="normal">Normal (Cor sólida)</option>
                                <option value="gradient">Gradiente Linear</option>
                                <option value="multi">Gradiente Multicolor</option>
                            </select>
                        </div>
                    </div>
                </div>

                <button type="submit" class="generate-btn" id="generateBtn">
                    <span class="btn-text">Gerar QR Code</span>
                    <div class="loading-spinner" id="loadingSpinner"></div>
                </button>
            </form>
        </div>

        <!-- Área de Resultado -->
        <div class="result-container">
            <div class="qr-placeholder" id="qrPlaceholder">
                <div class="qr-icon">
                    <svg width="120" height="120" viewBox="0 0 120 120" fill="none">
                        <!-- QR Code placeholder icon -->
                        <rect x="10" y="10" width="25" height="25" fill="#6b7280" rx="4"/>
                        <rect x="85" y="10" width="25" height="25" fill="#6b7280" rx="4"/>
                        <rect x="10" y="85" width="25" height="25" fill="#6b7280" rx="4"/>
                        
                        <rect x="15" y="15" width="15" height="15" fill="white" rx="2"/>
                        <rect x="90" y="15" width="15" height="15" fill="white" rx="2"/>
                        <rect x="15" y="90" width="15" height="15" fill="white" rx="2"/>
                        
                        <rect x="19" y="19" width="7" height="7" fill="#6b7280"/>
                        <rect x="94" y="19" width="7" height="7" fill="#6b7280"/>
                        <rect x="19" y="94" width="7" height="7" fill="#6b7280"/>
                        
                        <!-- Pattern dots -->
                        <rect x="45" y="45" width="5" height="5" fill="#6b7280"/>
                        <rect x="55" y="45" width="5" height="5" fill="#6b7280"/>
                        <rect x="65" y="45" width="5" height="5" fill="#6b7280"/>
                        <rect x="75" y="45" width="5" height="5" fill="#6b7280"/>
                        
                        <rect x="45" y="55" width="5" height="5" fill="#6b7280"/>
                        <rect x="65" y="55" width="5" height="5" fill="#6b7280"/>
                        
                        <rect x="45" y="65" width="5" height="5" fill="#6b7280"/>
                        <rect x="55" y="65" width="5" height="5" fill="#6b7280"/>
                        <rect x="65" y="65" width="5" height="5" fill="#6b7280"/>
                        <rect x="75" y="65" width="5" height="5" fill="#6b7280"/>
                    </svg>
                </div>
                <h3>Nenhum QR Code Gerado</h3>
                <p>Utilize o formulário ao lado para gerar seu QR Code PIX.</p>
            </div>

            <div class="qr-result" id="qrResult" style="display: none;">
                <div class="qr-image-container">
                    <img id="qrImage" src="" alt="QR Code PIX" />
                    <button class="download-btn" id="downloadBtn">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M8 1V11M8 11L11 8M8 11L5 8M2 13H14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Download
                    </button>
                </div>
                
                <div class="qr-info">
                    <h3>QR Code Gerado com Sucesso!</h3>
                    <div class="br-code-container">
                        <label>BR Code:</label>
                        <div class="br-code-text" id="brCodeText"></div>
                        <button class="copy-btn" id="copyBtn">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M4 4V2C4 1.44772 4.44772 1 5 1H13C13.5523 1 14 1.44772 14 2V10C14 10.5523 13.5523 11 13 11H11M10 5H3C2.44772 5 2 5.44772 2 6V13C2 13.5523 2.44772 14 3 14H10C10.5523 14 11 13.5523 11 13V6C11 5.44772 10.5523 5 10 5Z" stroke="currentColor" stroke-width="1.5"/>
                            </svg>
                            Copiar
                        </button>
                    </div>
                    
                    <div class="pix-details" id="pixDetails"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div class="modal" id="errorModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Erro</h3>
                <button class="close-btn" id="closeModal">&times;</button>
            </div>
            <div class="modal-body">
                <p id="errorMessage"></p>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>