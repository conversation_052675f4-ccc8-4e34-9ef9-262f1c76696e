// PIX QR Code Generator Frontend Application
class PixQRGenerator {
  constructor() {
    this.initializeElements();
    this.setupEventListeners();
    this.setupValidation();
    this.setupMasks();
  }

  initializeElements() {
    // Form elements
    this.form = document.getElementById('pixForm');
    this.keyTypeSelect = document.getElementById('keyType');
    this.pixKeyInput = document.getElementById('pixKey');
    this.receiverNameInput = document.getElementById('receiverName');
    this.receiverCityInput = document.getElementById('receiverCity');
    this.amountInput = document.getElementById('amount');
    this.referenceInput = document.getElementById('reference');
    this.descriptionInput = document.getElementById('description');

    // Style elements
    this.markerStyleSelect = document.getElementById('markerStyle');
    this.frameStyleSelect = document.getElementById('frameStyle');
    this.gradientColorSelect = document.getElementById('gradientColor');
    this.gradientModeSelect = document.getElementById('gradientMode');

    // UI elements
    this.generateBtn = document.getElementById('generateBtn');
    this.loadingSpinner = document.getElementById('loadingSpinner');
    this.qrPlaceholder = document.getElementById('qrPlaceholder');
    this.qrResult = document.getElementById('qrResult');
    this.qrImage = document.getElementById('qrImage');
    this.brCodeText = document.getElementById('brCodeText');
    this.pixDetails = document.getElementById('pixDetails');
    this.downloadBtn = document.getElementById('downloadBtn');
    this.copyBtn = document.getElementById('copyBtn');

    // Modal elements
    this.errorModal = document.getElementById('errorModal');
    this.errorMessage = document.getElementById('errorMessage');
    this.closeModal = document.getElementById('closeModal');

    // Validation element
    this.keyValidation = document.getElementById('keyValidation');

    // Current BR Code
    this.currentBRCode = '';
  }

  setupEventListeners() {
    // Form submission
    this.form.addEventListener('submit', (e) => this.handleFormSubmit(e));

    // Key type change
    this.keyTypeSelect.addEventListener('change', () => this.handleKeyTypeChange());

    // PIX key input validation
    this.pixKeyInput.addEventListener('input', () => this.validatePixKey());
    this.pixKeyInput.addEventListener('blur', () => this.validatePixKey());

    // Character counters
    this.receiverNameInput.addEventListener('input', () =>
      this.updateCharCounter(this.receiverNameInput, 25)
    );
    this.receiverCityInput.addEventListener('input', () =>
      this.updateCharCounter(this.receiverCityInput, 15)
    );

    // Buttons
    this.downloadBtn.addEventListener('click', () => this.downloadQRCode());
    this.copyBtn.addEventListener('click', () => this.copyBRCode());
    this.closeModal.addEventListener('click', () => this.hideModal());

    // Modal close on backdrop click
    this.errorModal.addEventListener('click', (e) => {
      if (e.target === this.errorModal) {
        this.hideModal();
      }
    });

    // ESC key to close modal
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.errorModal.style.display === 'block') {
        this.hideModal();
      }
    });
  }

  setupValidation() {
    // Initial key type setup
    this.handleKeyTypeChange();
  }

  setupMasks() {
    // Currency mask for amount input
    this.amountInput.addEventListener('input', (e) => {
      let value = e.target.value.replace(/\D/g, '');
      if (value.length === 0) {
        e.target.value = '';
        return;
      }

      value = (parseInt(value) / 100).toFixed(2);
      e.target.value = 'R$ ' + value.replace('.', ',');
    });

    // Reference input: only alphanumeric
    this.referenceInput.addEventListener('input', (e) => {
      e.target.value = e.target.value.replace(/[^A-Za-z0-9]/g, '');
    });
  }

  handleKeyTypeChange() {
    const keyType = this.keyTypeSelect.value;
    const pixKeyInput = this.pixKeyInput;

    // Clear previous value and validation
    pixKeyInput.value = '';
    this.keyValidation.textContent = '';
    this.keyValidation.className = 'validation-message';

    // Update placeholder and input type based on key type
    switch (keyType) {
      case 'cpf':
        pixKeyInput.placeholder = '000.000.000-00';
        pixKeyInput.type = 'text';
        pixKeyInput.maxLength = 14;
        this.setupCPFMask();
        break;
      case 'phone':
        pixKeyInput.placeholder = '(11) 99999-9999';
        pixKeyInput.type = 'tel';
        pixKeyInput.maxLength = 15;
        this.setupPhoneMask();
        break;
      case 'email':
        pixKeyInput.placeholder = '<EMAIL>';
        pixKeyInput.type = 'email';
        pixKeyInput.maxLength = 50;
        this.removeMask();
        break;
      case 'random':
        pixKeyInput.placeholder = 'chave-aleatoria-uuid';
        pixKeyInput.type = 'text';
        pixKeyInput.maxLength = 50;
        this.removeMask();
        break;
    }

    // Revalidate if there's already content
    if (pixKeyInput.value.trim()) {
      this.validatePixKey();
    }
  }

  setupCPFMask() {
    this.pixKeyInput.addEventListener(
      'input',
      (this.cpfMaskHandler = (e) => {
        let value = e.target.value.replace(/\D/g, '');
        value = value.replace(/(\d{3})(\d)/, '$1.$2');
        value = value.replace(/(\d{3})(\d)/, '$1.$2');
        value = value.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
        e.target.value = value;
      })
    );
  }

  setupPhoneMask() {
    this.pixKeyInput.addEventListener(
      'input',
      (this.phoneMaskHandler = (e) => {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length <= 10) {
          value = value.replace(/(\d{2})(\d)/, '($1) $2');
          value = value.replace(/(\d{4})(\d)/, '$1-$2');
        } else {
          value = value.replace(/(\d{2})(\d)/, '($1) $2');
          value = value.replace(/(\d{5})(\d)/, '$1-$2');
        }
        e.target.value = value;
      })
    );
  }

  removeMask() {
    if (this.cpfMaskHandler) {
      this.pixKeyInput.removeEventListener('input', this.cpfMaskHandler);
    }
    if (this.phoneMaskHandler) {
      this.pixKeyInput.removeEventListener('input', this.phoneMaskHandler);
    }
  }

  validatePixKey() {
    const keyType = this.keyTypeSelect.value;
    const value = this.pixKeyInput.value.trim();
    const validation = this.keyValidation;

    if (!value) {
      validation.textContent = '';
      validation.className = 'validation-message';
      return false;
    }

    let isValid = false;
    let message = '';

    switch (keyType) {
      case 'cpf':
        isValid = this.validateCPF(value);
        message = isValid ? 'CPF válido' : 'CPF inválido';
        break;
      case 'phone':
        isValid = this.validatePhone(value);
        message = isValid ? 'Telefone válido' : 'Telefone inválido';
        break;
      case 'email':
        isValid = this.validateEmail(value);
        message = isValid ? 'Email válido' : 'Email inválido';
        break;
      case 'random':
        isValid = value.length >= 10;
        message = isValid ? 'Chave válida' : 'Chave deve ter pelo menos 10 caracteres';
        break;
    }

    validation.textContent = message;
    validation.className = `validation-message ${isValid ? 'success' : 'error'}`;

    return isValid;
  }

  validateCPF(cpf) {
    cpf = cpf.replace(/\D/g, '');

    if (cpf.length !== 11) return false;
    if (/^(\d)\1{10}$/.test(cpf)) return false;

    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cpf.charAt(i)) * (10 - i);
    }
    let digit1 = ((sum * 10) % 11) % 10;

    if (digit1 !== parseInt(cpf.charAt(9))) return false;

    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cpf.charAt(i)) * (11 - i);
    }
    let digit2 = ((sum * 10) % 11) % 10;

    return digit2 === parseInt(cpf.charAt(10));
  }

  validatePhone(phone) {
    const digits = phone.replace(/\D/g, '');
    return digits.length === 10 || digits.length === 11;
  }

  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  updateCharCounter(input, maxLength) {
    const counter = input.parentElement.querySelector('.char-counter');
    if (counter) {
      counter.textContent = `${input.value.length}/${maxLength} caracteres`;
    }
  }

  async handleFormSubmit(e) {
    e.preventDefault();

    if (!this.validateForm()) {
      return;
    }

    this.setLoading(true);

    try {
      const formData = this.getFormData();
      const brCode = await this.generateBRCode(formData);
      const qrCodeData = await this.generateQRCode(brCode, formData.styles);

      this.displayResult(brCode, qrCodeData, formData);
      this.showToast('QR Code gerado com sucesso!', 'success');
    } catch (error) {
      console.error('Error generating QR Code:', error);
      this.showError('Erro ao gerar QR Code: ' + error.message);
    } finally {
      this.setLoading(false);
    }
  }

  validateForm() {
    const requiredFields = [
      { element: this.pixKeyInput, name: 'Chave PIX' },
      { element: this.receiverNameInput, name: 'Nome do recebedor' },
      { element: this.receiverCityInput, name: 'Cidade do recebedor' },
    ];

    for (const field of requiredFields) {
      if (!field.element.value.trim()) {
        this.showError(`O campo "${field.name}" é obrigatório.`);
        field.element.focus();
        return false;
      }
    }

    // Validate PIX key
    if (!this.validatePixKey()) {
      this.showError('Por favor, insira uma chave PIX válida.');
      this.pixKeyInput.focus();
      return false;
    }

    return true;
  }

  getFormData() {
    // Parse amount
    let amount = 0;
    if (this.amountInput.value.trim()) {
      const amountStr = this.amountInput.value.replace(/[^\d,]/g, '').replace(',', '.');
      amount = parseFloat(amountStr) || 0;
    }

    // Clean PIX key based on type
    let pixKey = this.pixKeyInput.value.trim();
    const keyType = this.keyTypeSelect.value;

    if (keyType === 'cpf') {
      pixKey = pixKey.replace(/\D/g, '');
    } else if (keyType === 'phone') {
      pixKey = pixKey.replace(/\D/g, '');
      if (!pixKey.startsWith('55')) {
        pixKey = '55' + pixKey;
      }
      if (!pixKey.startsWith('+')) {
        pixKey = '+' + pixKey;
      }
    }

    return {
      keyType,
      pixKey,
      receiverName: this.receiverNameInput.value.trim(),
      receiverCity: this.receiverCityInput.value.trim(),
      amount,
      reference: this.referenceInput.value.trim(),
      description: this.descriptionInput.value.trim(),
      styles: {
        markerStyle: this.markerStyleSelect.value,
        frameStyle: this.frameStyleSelect.value || null,
        gradientColor: this.gradientColorSelect.value,
        gradientMode: this.gradientModeSelect.value,
      },
    };
  }

  // PIX library functions using local qr-pix implementation
  async generateBRCode(data) {
    // Generate BR-Code using local qr-pix library implementation

    const getValue = (tag, value) => {
      return `${tag}${value.length.toString().padStart(2, '0')}${value}`;
    };

    const formattedText = (text) => {
      return text
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '');
    };

    const crcCompute = (data) => {
      // Simplified CRC16 calculation (use actual implementation in production)
      let crc = 0xffff;
      const dataBytes = new TextEncoder().encode(data);

      for (const byte of dataBytes) {
        crc ^= byte << 8;
        for (let i = 0; i < 8; i++) {
          if (crc & 0x8000) {
            crc = (crc << 1) ^ 0x1021;
          } else {
            crc <<= 1;
          }
        }
      }

      return (crc & 0xffff).toString(16).toUpperCase().padStart(4, '0');
    };

    // Build account information
    const basePix = getValue('00', 'br.gov.bcb.pix');
    let infoString = getValue('01', data.pixKey);

    if (data.description) {
      infoString += getValue('02', formattedText(data.description));
    }

    const accountInfo = getValue('26', basePix + infoString);

    // Build additional data field
    const txid = data.reference || '***';
    const additionalData = getValue('62', getValue('05', formattedText(txid)));

    // Build complete BR Code
    let resultString = getValue('00', '01'); // Payload Format Indicator
    resultString += getValue('01', '11'); // Point of Initiation Method (static)
    resultString += accountInfo;
    resultString += getValue('52', '0000'); // Merchant Category Code
    resultString += getValue('53', '986'); // Transaction Currency (BRL)

    if (data.amount > 0) {
      resultString += getValue('54', data.amount.toFixed(2));
    }

    resultString += getValue('58', 'BR'); // Country Code
    resultString += getValue('59', formattedText(data.receiverName)); // Merchant Name
    resultString += getValue('60', formattedText(data.receiverCity)); // Merchant City
    resultString += additionalData;
    resultString += '6304'; // CRC placeholder

    const finalBRCode = resultString + crcCompute(resultString);

    return finalBRCode;
  }

  async generateQRCode(brCode, styles) {
    try {
      // Generate QR code using local implementation
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Set canvas size
      const size = 300;
      canvas.width = size;
      canvas.height = size;

      // Generate QR code using a simple implementation
      const qrCode = this.createQRMatrix(brCode);
      const moduleSize = Math.floor(size / qrCode.length);
      const offset = (size - qrCode.length * moduleSize) / 2;

      // Clear canvas with white background
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, size, size);

      // Draw QR code modules
      ctx.fillStyle = '#000000';
      for (let row = 0; row < qrCode.length; row++) {
        for (let col = 0; col < qrCode[row].length; col++) {
          if (qrCode[row][col]) {
            ctx.fillRect(
              offset + col * moduleSize,
              offset + row * moduleSize,
              moduleSize,
              moduleSize
            );
          }
        }
      }

      // Convert canvas to base64
      return canvas.toDataURL('image/png');
    } catch (error) {
      console.error('QR Code generation error:', error);
      throw new Error('Não foi possível gerar o QR Code. Tente novamente.');
    }
  }

  createQRMatrix(data) {
    // Simple QR code matrix generation
    // This is a simplified implementation for demonstration
    // In a real implementation, you would use a proper QR code library

    try {
      // Use a basic QR code generation approach
      // For now, we'll create a simple pattern that represents the data
      const size = 25; // QR code modules
      const matrix = Array(size)
        .fill()
        .map(() => Array(size).fill(false));

      // Add finder patterns (corners)
      this.addFinderPattern(matrix, 0, 0);
      this.addFinderPattern(matrix, 0, size - 7);
      this.addFinderPattern(matrix, size - 7, 0);

      // Add timing patterns
      for (let i = 8; i < size - 8; i++) {
        matrix[6][i] = i % 2 === 0;
        matrix[i][6] = i % 2 === 0;
      }

      // Add data modules (simplified pattern based on data hash)
      const hash = this.simpleHash(data);
      for (let row = 0; row < size; row++) {
        for (let col = 0; col < size; col++) {
          if (!this.isReservedModule(row, col, size)) {
            matrix[row][col] = (hash + row + col) % 3 === 0;
          }
        }
      }

      return matrix;
    } catch (error) {
      console.error('Error creating QR matrix:', error);
      // Return a simple fallback pattern
      return this.createFallbackMatrix();
    }
  }

  addFinderPattern(matrix, startRow, startCol) {
    const pattern = [
      [1, 1, 1, 1, 1, 1, 1],
      [1, 0, 0, 0, 0, 0, 1],
      [1, 0, 1, 1, 1, 0, 1],
      [1, 0, 1, 1, 1, 0, 1],
      [1, 0, 1, 1, 1, 0, 1],
      [1, 0, 0, 0, 0, 0, 1],
      [1, 1, 1, 1, 1, 1, 1],
    ];

    for (let row = 0; row < 7; row++) {
      for (let col = 0; col < 7; col++) {
        if (startRow + row < matrix.length && startCol + col < matrix[0].length) {
          matrix[startRow + row][startCol + col] = pattern[row][col] === 1;
        }
      }
    }
  }

  isReservedModule(row, col, size) {
    // Check if module is part of finder patterns
    if ((row < 9 && col < 9) || (row < 9 && col >= size - 8) || (row >= size - 8 && col < 9)) {
      return true;
    }

    // Check if module is part of timing patterns
    if (row === 6 || col === 6) {
      return true;
    }

    return false;
  }

  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  createFallbackMatrix() {
    const size = 25;
    const matrix = Array(size)
      .fill()
      .map(() => Array(size).fill(false));

    // Create a simple checkerboard pattern
    for (let row = 0; row < size; row++) {
      for (let col = 0; col < size; col++) {
        matrix[row][col] = (row + col) % 2 === 0;
      }
    }

    return matrix;
  }

  blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  displayResult(brCode, qrCodeData, formData) {
    this.currentBRCode = brCode;

    // Hide placeholder and show result
    this.qrPlaceholder.style.display = 'none';
    this.qrResult.style.display = 'block';

    // Set QR code image
    this.qrImage.src = qrCodeData;
    this.qrImage.alt = 'QR Code PIX';

    // Set BR Code text
    this.brCodeText.textContent = brCode;

    // Display PIX details
    this.displayPixDetails(formData);

    // Scroll to result
    this.qrResult.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }

  displayPixDetails(data) {
    const details = [
      { label: 'Recebedor', value: data.receiverName },
      { label: 'Cidade', value: data.receiverCity },
      { label: 'Chave PIX', value: data.pixKey },
      {
        label: 'Valor',
        value: data.amount > 0 ? `R$ ${data.amount.toFixed(2).replace('.', ',')}` : 'Valor livre',
      },
    ];

    if (data.reference) {
      details.push({ label: 'Referência', value: data.reference });
    }

    if (data.description) {
      details.push({ label: 'Descrição', value: data.description });
    }

    const detailsHTML = details
      .map(
        (item) => `
            <div class="pix-detail-item">
                <span class="pix-detail-label">${item.label}:</span>
                <span class="pix-detail-value">${item.value}</span>
            </div>
        `
      )
      .join('');

    this.pixDetails.innerHTML = detailsHTML;
  }

  downloadQRCode() {
    if (!this.qrImage.src) return;

    const link = document.createElement('a');
    link.download = 'qrcode-pix.png';
    link.href = this.qrImage.src;
    link.click();

    this.showToast('QR Code baixado com sucesso!', 'success');
  }

  async copyBRCode() {
    if (!this.currentBRCode) return;

    try {
      await navigator.clipboard.writeText(this.currentBRCode);
      this.showToast('BR Code copiado para a área de transferência!', 'success');
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = this.currentBRCode;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);

      this.showToast('BR Code copiado!', 'success');
    }
  }

  setLoading(loading) {
    this.generateBtn.disabled = loading;
    this.generateBtn.classList.toggle('loading', loading);

    if (loading) {
      this.loadingSpinner.style.display = 'block';
    } else {
      this.loadingSpinner.style.display = 'none';
    }
  }

  showError(message) {
    this.errorMessage.textContent = message;
    this.errorModal.style.display = 'block';
  }

  hideModal() {
    this.errorModal.style.display = 'none';
  }

  showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;

    document.body.appendChild(toast);

    // Show toast
    setTimeout(() => toast.classList.add('show'), 100);

    // Hide toast
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
  }
}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new PixQRGenerator();
});
