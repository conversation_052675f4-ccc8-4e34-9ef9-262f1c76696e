# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test coverage
coverage/
*.lcov

# Runtime files
*.log
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Generated QR codes (examples)
*.png
*.gif
!assets/*.png
!assets/*.gif

# Temporary files
tmp/
temp/