# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development Commands
- `npm run build` - Compile TypeScript to JavaScript in dist/
- `npm run dev` - Start development server at http://localhost:3000
- `npm run serve` - Start web interface server at http://localhost:3000
- `npm test` - Run Jest test suite
- `npm run lint` - Lint TypeScript files with ESLint
- `npm run format` - Format code with Prettier

### Testing
- Test files are located in `tests/` directory
- Uses Jest with ts-jest preset for TypeScript support
- Run single test: `npm test -- pix.test.ts`
- Coverage reports generated in `coverage/` directory

## Project Architecture

### Core Components

**Main PIX Class** (`src/pix.ts`)
- Primary interface for PIX BR-Code generation
- Handles PIX key validation (CPF, phone, email, random UUID)
- Manages transaction data: receiver info, amount, description
- Supports both static and dynamic PIX transactions
- Integrates with QR code generator for visual output

**QR Code Generator** (`src/core/qrgen.ts`)
- Advanced QR code styling and customization engine
- Supports multiple marker styles (square, circle, rounded, star, etc.)
- Gradient effects with linear and radial modes
- Frame overlays (clean, tech, creative themes)
- Center image/GIF integration for animated QR codes
- Built on Canvas API and Sharp for image processing

**Utilities Structure**
- `src/core/utils/validators.ts` - CPF and phone number validation
- `src/core/utils/pixParser.ts` - BR-Code parsing and CRC16 computation
- `src/core/utils/services.ts` - TLV formatting and text processing
- `src/core/utils/imageUtils.ts` - Image processing utilities (SVG to PNG, image compositing)

**Styling System**
- `src/core/styles/` - SVG definitions for QR code visual elements
- Modular styling with enums for different visual options
- Supports Brazilian payment branding themes

### Key Technical Patterns

**BR-Code Generation**
- Follows PIX technical specifications with TLV (Tag-Length-Value) encoding
- Automatic CRC16 checksum calculation for data integrity
- Supports both static PIX (fixed receiver) and dynamic PIX (PSP URLs)

**Image Processing Pipeline**
- Canvas-based QR generation with qrcode library
- Sharp for advanced image operations (gradients, compositing)
- GIF frame extraction and animation support
- SVG-to-PNG conversion for custom styling elements

**TypeScript Configuration**
- Target ES2020 with CommonJS modules
- Strict mode enabled with comprehensive type checking
- Declaration files generated for library distribution
- Source maps for debugging support

## Development Notes

### Dependencies
- **Canvas**: Requires system-level dependencies for image processing
- **Sharp**: High-performance image manipulation
- **QRCode**: Base QR code generation functionality
- **JIMP/GIF-frames**: Animation and GIF processing support

### Platform Considerations
- macOS: Canvas dependencies usually install automatically
- Linux: Requires build-essential and Cairo/Pango libraries
- Windows: May need specific Canvas binary mirrors

### Code Style
- ESLint configuration allows console.log (common for library examples)
- Prettier enforces consistent formatting (single quotes, semicolons)
- TypeScript strict mode with explicit any warnings
- 100-character line width limit