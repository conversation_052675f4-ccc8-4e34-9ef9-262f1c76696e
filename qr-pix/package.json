{"name": "pypix-ts", "version": "1.0.0", "description": "TypeScript/JavaScript port of PyPix - Brazilian PIX QR Code generator with advanced styling", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "node server.js", "serve": "node server.js", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["pix", "qrcode", "brazil", "payment", "brcode"], "author": "Ported from PyPix by Cleiton Leonel", "license": "MIT", "dependencies": {"qrcode": "^1.5.3", "canvas": "^2.11.2", "sharp": "^0.33.0", "gif-frames": "^1.0.1", "jimp": "^0.22.10"}, "devDependencies": {"@types/node": "^20.0.0", "@types/qrcode": "^1.5.5", "typescript": "^5.0.0", "ts-node": "^10.9.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "ts-jest": "^29.0.0", "eslint": "^8.0.0", "@typescript-eslint/parser": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "prettier": "^3.0.0"}, "engines": {"node": ">=16.0.0"}}