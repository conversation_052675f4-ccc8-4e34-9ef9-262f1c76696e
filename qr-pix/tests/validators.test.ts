import { validateCPF, validatePhone } from '../src/core/utils/validators';

describe('Validators', () => {
  describe('validateCPF', () => {
    test('should validate correct CPF', () => {
      expect(validateCPF('12345678909')).toBe(true);
      expect(validateCPF('11144477735')).toBe(true);
    });

    test('should reject invalid CPF', () => {
      expect(validateCPF('12345678901')).toBe(false);
      expect(validateCPF('00000000000')).toBe(false);
      expect(validateCPF('11111111111')).toBe(false);
    });

    test('should handle CPF with formatting', () => {
      expect(validateCPF('123.456.789-09')).toBe(true);
      expect(validateCPF('111.444.777-35')).toBe(true);
    });

    test('should reject CPF with wrong length', () => {
      expect(validateCPF('123456789')).toBe(false);
      expect(validateCPF('123456789012')).toBe(false);
    });

    test('should reject non-numeric characters', () => {
      expect(validateCPF('12345678abc')).toBe(false);
    });
  });

  describe('validatePhone', () => {
    test('should validate Brazilian phone numbers', () => {
      expect(validatePhone('+5511999887766')).toBe(true);
      expect(validatePhone('11999887766')).toBe(true);
      expect(validatePhone('+552799577291')).toBe(true);
    });

    test('should validate international phone format', () => {
      expect(validatePhone('+1234567890123')).toBe(true);
      expect(validatePhone('1234567890')).toBe(true);
    });

    test('should reject invalid phone numbers', () => {
      expect(validatePhone('0123456789')).toBe(false); // Starts with 0
      expect(validatePhone('+01234567890')).toBe(false); // Starts with 0
      expect(validatePhone('123')).toBe(false); // Too short
      expect(validatePhone('+123456789012345678')).toBe(false); // Too long
    });

    test('should reject non-numeric characters', () => {
      expect(validatePhone('11999887766abc')).toBe(false);
      expect(validatePhone('+5511999887766@')).toBe(false);
    });

    test('should handle empty or invalid input', () => {
      expect(validatePhone('')).toBe(false);
      expect(validatePhone('+')).toBe(false);
      expect(validatePhone('abc')).toBe(false);
    });
  });
});