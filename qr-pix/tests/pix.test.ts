import { Pix, parseBrCode, MarkerStyle, BorderStyle, LineStyle, FrameStyle, GradientMode } from '../src';

describe('Pix', () => {
  let pix: Pix;

  beforeEach(() => {
    pix = new Pix();
  });

  describe('Configuration', () => {
    test('should set receiver name with validation', () => {
      pix.setNameReceiver('<PERSON> Silva');
      expect(pix.getBrCode()).toContain('João Silva');
    });

    test('should throw error for receiver name too long', () => {
      expect(() => {
        pix.setNameReceiver('This name is definitely too long for PIX receiver name field');
      }).toThrow('The maximum number of characters for the receiver name is 25.');
    });

    test('should set city with validation', () => {
      pix.setCityReceiver('São Paulo');
      expect(pix.getBrCode()).toContain('Sao Paulo'); // Formatted text removes accents
    });

    test('should throw error for city name too long', () => {
      expect(() => {
        pix.setCityReceiver('Very Long City Name That Exceeds Limit');
      }).toThrow('The maximum number of characters for the receiver city is 15.');
    });

    test('should set amount with validation', () => {
      pix.setAmount(10.50);
      const brCode = pix.getBrCode();
      expect(brCode).toContain('10.50');
    });

    test('should configure via constructor', () => {
      const configuredPix = new Pix({
        nameReceiver: 'Test User',
        cityReceiver: 'Test City',
        key: '+5511999887766',
        amount: 25.00,
        description: 'Test payment'
      });

      const brCode = configuredPix.getBrCode();
      expect(brCode).toContain('Test User');
      expect(brCode).toContain('Test City');
      expect(brCode).toContain('25.00');
    });
  });

  describe('PIX Key Validation', () => {
    test('should handle phone number key', () => {
      pix.setKey('11999887766');
      pix.setNameReceiver('Test');
      pix.setCityReceiver('City');
      
      const brCode = pix.getBrCode();
      expect(brCode).toContain('+5511999887766');
    });

    test('should handle CPF key', () => {
      pix.setKey('12345678909'); // Valid CPF format
      pix.setNameReceiver('Test');
      pix.setCityReceiver('City');
      
      const brCode = pix.getBrCode();
      expect(brCode).toBeTruthy();
    });

    test('should handle random key', () => {
      pix.setKey('b5fe1edc-d108-410f-b966-eccaaca75e4f');
      pix.setNameReceiver('Test');
      pix.setCityReceiver('City');
      
      const brCode = pix.getBrCode();
      expect(brCode).toContain('b5fe1edc-d108-410f-b966-eccaaca75e4f');
    });

    test('should throw error when no key or URL provided', () => {
      pix.setNameReceiver('Test');
      pix.setCityReceiver('City');
      
      expect(() => {
        pix.getBrCode();
      }).toThrow('You must enter a URL or a PIX key.');
    });
  });

  describe('BR-Code Generation', () => {
    test('should generate valid static PIX code', () => {
      pix.setNameReceiver('Test User');
      pix.setCityReceiver('Test City');
      pix.setKey('+5511999887766');
      pix.setAmount(10.00);
      pix.setDescription('Test payment');
      pix.setIdentification('TEST001');

      const brCode = pix.getBrCode();
      
      // Should start with payload format indicator
      expect(brCode).toMatch(/^00020101/);
      // Should end with CRC (4 hex digits)
      expect(brCode).toMatch(/[0-9A-F]{4}$/);
      // Should contain BR country code
      expect(brCode).toContain('5802BR');
    });

    test('should generate dynamic PIX code', () => {
      pix.setNameReceiver('Test User');
      pix.setCityReceiver('Test City');
      pix.setDefaultUrlPix('psp.example.com/payment/123');
      pix.isSingleTransaction(true);

      const brCode = pix.getBrCode();
      
      // Should have dynamic initiation method (12)
      expect(brCode).toContain('010212');
      expect(brCode).toContain('psp.example.com/payment/123');
    });

    test('should handle zero amount (open value)', () => {
      pix.setNameReceiver('Test User');
      pix.setCityReceiver('Test City');
      pix.setKey('+5511999887766');
      pix.setAmount(0);

      const brCode = pix.getBrCode();
      expect(brCode).toContain('0.00');
    });
  });

  describe('BR-Code Parsing', () => {
    test('should parse valid BR-Code', () => {
      // Generate a BR-Code first
      pix.setNameReceiver('Test User');
      pix.setCityReceiver('Test City');
      pix.setKey('+5511999887766');
      pix.setAmount(10.00);
      pix.setDescription('Test payment');
      pix.setIdentification('TEST001');

      const brCode = pix.getBrCode();
      const parsed = parseBrCode(brCode);

      expect(parsed.nome).toBe('Test User');
      expect(parsed.cidade).toBe('Test City');
      expect(parsed.valor).toBe(10.00);
      expect(parsed.chave).toBe('+5511999887766');
      expect(parsed.txid).toBe('TEST001');
      expect(parsed.descricao).toBe('Test payment');
    });

    test('should validate CRC in parsed BR-Code', () => {
      const validBrCode = pix.getBrCode();
      if (validBrCode) {
        expect(() => parseBrCode(validBrCode)).not.toThrow();
      }
    });

    test('should throw error for invalid CRC', () => {
      const invalidBrCode = '00020101021126690014br.gov.bcb.pix0114+55279957722910229Doacao Livre / QRCODE - PYPIX52040000530398654045.005802BR5905Teste6009Cariacica61082914861362130509PIXMP0001630499999'; // Invalid CRC
      
      expect(() => {
        parseBrCode(invalidBrCode);
      }).toThrow('CRC validation failed');
    });
  });
});