import { parseBrCode, crcCompute } from '../src/core/utils/pixParser';

describe('PIX Parser', () => {
  describe('crcCompute', () => {
    test('should compute correct CRC16', () => {
      const testData = '00020101021126690014br.gov.bcb.pix0114+55279957722910229Doacao Livre / QRCODE - PYPIX52040000530398654045.005802BR5905Teste6009Cariacica61082914861362130509PIXMP00016304';
      const expectedCRC = '325F';
      
      expect(crcCompute(testData)).toBe(expectedCRC);
    });

    test('should handle empty string', () => {
      expect(crcCompute('')).toBe('FFFF');
    });

    test('should handle simple strings', () => {
      expect(crcCompute('test')).toMatch(/^[0-9A-F]{4}$/);
    });
  });

  describe('parseBrCode', () => {
    const validBrCodes = [
      {
        name: 'phone with amount',
        brCode: '00020101021126690014br.gov.bcb.pix0114+55279957722910229Doacao Livre / QRCODE - PYPIX52040000530398654045.005802BR5905Teste6009Cariacica61082914861362130509PIXMP00016304325F',
        expected: {
          nome: 'Teste',
          cidade: 'Cariacica',
          valor: 5.00,
          chave: '+5527995772291',
          txid: 'PIXMP0001'
        }
      },
      {
        name: 'random key with zero amount',
        brCode: '00020101021126910014br.gov.bcb.pix0136b5fe1edc-d108-410f-b966-eccaaca75e4f0229Doacao Livre / QRCODE - PYPIX52040000530398654030.05802BR5921Cleiton Leonel Creton6009Cariacica62070503***63049182',
        expected: {
          nome: 'Cleiton Leonel Creton',
          cidade: 'Cariacica',
          valor: 0.0,
          chave: 'b5fe1edc-d108-410f-b966-eccaaca75e4f',
          txid: '***'
        }
      }
    ];

    validBrCodes.forEach(({ name, brCode, expected }) => {
      test(`should parse ${name}`, () => {
        const result = parseBrCode(brCode);
        
        expect(result.nome).toBe(expected.nome);
        expect(result.cidade).toBe(expected.cidade);
        expect(result.valor).toBe(expected.valor);
        expect(result.chave).toBe(expected.chave);
        expect(result.txid).toBe(expected.txid);
      });
    });

    test('should include raw parsed data', () => {
      const brCode = validBrCodes[0].brCode;
      const result = parseBrCode(brCode);
      
      expect(result._raw).toBeDefined();
      expect(result._raw?.dados_gerais).toBeDefined();
      expect(result._raw?.dados_gerais.payload_format_indicator).toBe('01');
      expect(result._raw?.dados_gerais.country_code).toBe('BR');
      expect(result._raw?.crc_valid).toBe(true);
    });

    test('should throw error for invalid CRC field', () => {
      const invalidBrCode = '00020101021126690014br.gov.bcb.pix0114+55279957722910229Doacao';
      
      expect(() => {
        parseBrCode(invalidBrCode);
      }).toThrow('Invalid PIX string: CRC field (ID 63) not found or malformed.');
    });

    test('should throw error for CRC validation failure', () => {
      const brCodeWithBadCRC = '00020101021126690014br.gov.bcb.pix0114+55279957722910229Doacao Livre / QRCODE - PYPIX52040000530398654045.005802BR5905Teste6009Cariacica61082914861362130509PIXMP000163040000';
      
      expect(() => {
        parseBrCode(brCodeWithBadCRC);
      }).toThrow('CRC validation failed');
    });

    test('should handle missing optional fields gracefully', () => {
      // Create a minimal valid BR code (this would need to be constructed properly)
      const minimalBrCode = '000201010211269014br.gov.bcb.pix0114+55279957722915204000053039865802BR5905Teste6009Cariacica62070503***63046B4A';
      
      const result = parseBrCode(minimalBrCode);
      expect(result.nome).toBe('Teste');
      expect(result.cidade).toBe('Cariacica');
      expect(result.chave).toBe('+5527995772291');
    });
  });
});