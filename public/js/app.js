// PIX QR Code Generator with Advanced Customization
// ATUALIZAÇÃO: Implementada padronização visual para consistência entre QR Codes
// - Sempre usa qr-code-styling quando disponível
// - Configurações padronizadas para QR Codes sem personalização
// - Nível de correção de erro padronizado (M) para consistência visual
class PixQRGeneratorIntegrated {
  constructor() {
    this.initializeElements();
    this.setupEventListeners();
    this.setupValidation();
    this.setupMasks();
    this.setupCustomization();
    this.currentBRCode = '';
    this.qrCode = null;
    this.currentImageFile = null;
    this.exportOptions = null;
  }

  initializeElements() {
    // Form elements
    this.form = document.getElementById('pixForm');
    this.keyTypeSelect = document.getElementById('keyType');
    this.pixKeyInput = document.getElementById('pixKey');
    this.receiverNameInput = document.getElementById('receiverName');
    this.receiverCityInput = document.getElementById('receiverCity');
    this.amountInput = document.getElementById('amount');
    this.referenceInput = document.getElementById('reference');
    this.descriptionInput = document.getElementById('description');

    // UI elements
    this.generateBtn = document.getElementById('generateBtn');
    this.loadingSpinner = document.getElementById('loadingSpinner');
    this.qrPlaceholder = document.getElementById('qrPlaceholder');
    this.qrResult = document.getElementById('qrResult');
    this.qrPreview = document.getElementById('qr-preview');
    this.brCodeText = document.getElementById('brCodeText');
    this.pixDetails = document.getElementById('pixDetails');
    this.downloadBtn = document.getElementById('downloadBtn');
    this.downloadSvgBtn = document.getElementById('downloadSvgBtn');
    this.copyBtn = document.getElementById('copyBtn');

    // Modal elements
    this.errorModal = document.getElementById('errorModal');
    this.errorMessage = document.getElementById('errorMessage');
    this.closeModal = document.getElementById('closeModal');

    // Validation element
    this.keyValidation = document.getElementById('keyValidation');

    // Customization elements
    this.customizationToggle = document.getElementById('customizationToggle');
    this.customizationPanel = document.getElementById('customizationPanel');
  }

  setupCustomization() {
    // Toggle switch
    this.customizationToggle.addEventListener('click', () => {
      this.customizationToggle.classList.toggle('active');
      this.customizationPanel.classList.toggle('active');
    });

    // Customization controls event listeners
    const customizationInputs = [
      'dots-type',
      'dots-color',
      'corner-square-type',
      'corner-square-color',
      'corner-dot-type',
      'corner-dot-color',
      'background-color',
      'qr-size',
      'image-size',
      'image-margin',
      'hide-background-dots',
    ];

    customizationInputs.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        if (element.type === 'range') {
          element.addEventListener('input', () => this.updateRangeValue(id));
        }

        // Add handler for unified size control
        if (id === 'qr-size') {
          element.addEventListener('change', (e) => this.handleSizeChange(e));
        }

        element.addEventListener('change', () => this.generateQRIfReady());
        element.addEventListener('input', () => this.generateQRIfReady());
      }
    });

    // Setup margin button group
    this.setupMarginButtons();

    // Initialize margin container class
    this.updatePreviewContainerMargin();

    // Image upload
    const centerImage = document.getElementById('center-image');
    if (centerImage) {
      centerImage.addEventListener('change', (e) => this.handleImageUpload(e));
    }

    // Initialize range values
    this.updateRangeValue('image-size');
  }

  setupMarginButtons() {
    const marginButtons = document.querySelectorAll('.margin-btn');
    marginButtons.forEach((button) => {
      button.addEventListener('click', (e) => {
        e.preventDefault();

        // Remove active class from all buttons
        marginButtons.forEach((btn) => btn.classList.remove('active'));

        // Add active class to clicked button
        button.classList.add('active');

        // Update container padding based on margin selection
        this.updatePreviewContainerMargin();

        // Regenerate QR code with new margin
        this.generateQRIfReady();
      });
    });
  }

  updatePreviewContainerMargin() {
    const qrPreview = this.qrPreview;
    const currentMargin = this.getCurrentMargin();

    console.log(
      'updatePreviewContainerMargin - current margin:',
      currentMargin
    );
    console.log('qrPreview element:', qrPreview);

    // Remove existing margin classes
    qrPreview.classList.remove('margin-none', 'margin-default', 'margin-wide');

    // Add appropriate margin class
    switch (currentMargin) {
      case 0:
        qrPreview.classList.add('margin-none');
        console.log('Added margin-none class');
        break;
      case 10:
        qrPreview.classList.add('margin-default');
        console.log('Added margin-default class');
        break;
      case 30:
        qrPreview.classList.add('margin-wide');
        console.log('Added margin-wide class');
        break;
    }

    console.log('Final classList:', qrPreview.classList.toString());
  }

  getCurrentMargin() {
    const activeMarginBtn = document.querySelector('.margin-btn.active');
    const margin = activeMarginBtn
      ? parseInt(activeMarginBtn.dataset.margin)
      : 10;

    // Handle margin 0 issue - some QR libraries have minimum margin requirements
    if (margin === 0) {
      console.log('Using margin 0 with forced settings');
      return 0; // Keep 0, but we'll handle it differently
    }

    console.log('getCurrentMargin returning:', margin);
    return margin;
  }

  // PADRONIZAÇÃO: Função auxiliar para gerar configurações consistentes de QR code
  getStandardQROptions(data, width, height, margin, type = 'svg') {
    const baseOptions = {
      width: width,
      height: height,
      type: type,
      data: data,
      margin: margin,
      qrOptions: {
        typeNumber: 0, // Auto-detect para consistência
        mode: undefined,
        errorCorrectionLevel: 'M', // PADRÃO: Sempre usar nível M para consistência visual
      },
      dotsOptions: {
        color: document.getElementById('dots-color').value || '#000000',
        type: document.getElementById('dots-type').value || 'square',
        roundSize: true, // PADRÃO: Sempre usar roundSize para melhor qualidade
      },
      backgroundOptions: {
        color: document.getElementById('background-color').value || '#ffffff',
        round: 0,
      },
      cornersSquareOptions: {
        color:
          document.getElementById('corner-square-color').value || '#000000',
        type: document.getElementById('corner-square-type').value || undefined,
      },
      cornersDotOptions: {
        color: document.getElementById('corner-dot-color').value || '#000000',
        type: document.getElementById('corner-dot-type').value || undefined,
      },
    };

    // Adicionar imageOptions apenas se houver imagem
    if (this.currentImageFile) {
      baseOptions.imageOptions = {
        crossOrigin: 'anonymous',
        margin: parseInt(document.getElementById('image-margin').value) || 0,
        imageSize:
          parseFloat(document.getElementById('image-size').value) || 0.4,
        hideBackgroundDots: document.getElementById('hide-background-dots')
          .checked,
        saveAsBlob: true, // PADRÃO: Sempre usar blob para melhor qualidade
      };
    }

    return baseOptions;
  }

  updateRangeValue(inputId) {
    const input = document.getElementById(inputId);
    if (!input) return;

    let valueElement;
    let displayValue;

    switch (inputId) {
      case 'image-size':
        valueElement = document.getElementById('image-size-value');
        displayValue = Math.round(input.value * 100) + '%';
        break;
    }

    if (valueElement) {
      valueElement.textContent = displayValue;
    }
  }

  handleSizeChange(event) {
    const size = parseInt(event.target.value);

    // Atualizar os campos ocultos de largura e altura para manter compatibilidade
    const widthField = document.getElementById('qr-width');
    const heightField = document.getElementById('qr-height');

    if (widthField) widthField.value = size;
    if (heightField) heightField.value = size;

    // Regenerar QR Code se já estiver sendo exibido
    this.generateQRIfReady();
  }

  handleImageUpload(event) {
    const file = event.target.files[0];
    if (file) {
      this.currentImageFile = file;
      const reader = new FileReader();
      reader.onload = () => {
        this.generateQRIfReady();
      };
      reader.readAsDataURL(file);

      // Update label
      const label = document.querySelector('.image-upload-label');
      if (label) {
        label.innerHTML = `✅ ${file.name}<br><small>Clique para alterar</small>`;
      }
    }
  }

  setupEventListeners() {
    // Form submission
    this.form.addEventListener('submit', (e) => this.handleFormSubmit(e));

    // Key type change
    this.keyTypeSelect.addEventListener('change', () =>
      this.handleKeyTypeChange()
    );

    // PIX key input validation
    this.pixKeyInput.addEventListener('input', () => this.validatePixKey());
    this.pixKeyInput.addEventListener('blur', () => this.validatePixKey());

    // Character counters
    this.receiverNameInput.addEventListener('input', () =>
      this.updateCharCounter(this.receiverNameInput, 25)
    );
    this.receiverCityInput.addEventListener('input', () =>
      this.updateCharCounter(this.receiverCityInput, 15)
    );

    // Buttons
    this.downloadBtn.addEventListener('click', () =>
      this.downloadQRCode('png')
    );
    this.downloadSvgBtn.addEventListener('click', () =>
      this.downloadQRCode('svg')
    );
    this.copyBtn.addEventListener('click', () => this.copyBRCode());
    this.closeModal.addEventListener('click', () => this.hideModal());

    // Modal close on backdrop click
    this.errorModal.addEventListener('click', (e) => {
      if (e.target === this.errorModal) {
        this.hideModal();
      }
    });

    // ESC key to close modal
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.errorModal.style.display === 'block') {
        this.hideModal();
      }
    });
  }

  setupValidation() {
    // Initial key type setup
    this.handleKeyTypeChange();
  }

  setupMasks() {
    // Currency mask for amount input
    this.amountInput.addEventListener('input', (e) => {
      let value = e.target.value.replace(/\D/g, '');
      if (value.length === 0) {
        e.target.value = '';
        return;
      }

      value = (parseInt(value) / 100).toFixed(2);
      e.target.value = 'R$ ' + value.replace('.', ',');
    });

    // Reference input: only alphanumeric
    this.referenceInput.addEventListener('input', (e) => {
      e.target.value = e.target.value.replace(/[^A-Za-z0-9]/g, '');
    });
  }

  handleKeyTypeChange() {
    const keyType = this.keyTypeSelect.value;
    const pixKeyInput = this.pixKeyInput;

    // Clear previous value and validation
    pixKeyInput.value = '';
    this.keyValidation.textContent = '';
    this.keyValidation.className = 'validation-message';

    // Update placeholder and input type based on key type
    switch (keyType) {
      case 'cpf':
        pixKeyInput.placeholder = '000.000.000-00';
        pixKeyInput.type = 'text';
        pixKeyInput.maxLength = 14;
        this.setupCPFMask();
        break;
      case 'phone':
        pixKeyInput.placeholder = '(11) 99999-9999';
        pixKeyInput.type = 'tel';
        pixKeyInput.maxLength = 15;
        this.setupPhoneMask();
        break;
      case 'email':
        pixKeyInput.placeholder = '<EMAIL>';
        pixKeyInput.type = 'email';
        pixKeyInput.maxLength = 50;
        this.removeMask();
        break;
      case 'random':
        pixKeyInput.placeholder = 'chave-aleatoria-uuid';
        pixKeyInput.type = 'text';
        pixKeyInput.maxLength = 50;
        this.removeMask();
        break;
    }

    // Revalidate if there's already content
    if (pixKeyInput.value.trim()) {
      this.validatePixKey();
    }
  }

  setupCPFMask() {
    this.pixKeyInput.addEventListener(
      'input',
      (this.cpfMaskHandler = (e) => {
        let value = e.target.value.replace(/\D/g, '');
        value = value.replace(/(\d{3})(\d)/, '$1.$2');
        value = value.replace(/(\d{3})(\d)/, '$1.$2');
        value = value.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
        e.target.value = value;
      })
    );
  }

  setupPhoneMask() {
    this.pixKeyInput.addEventListener(
      'input',
      (this.phoneMaskHandler = (e) => {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length <= 10) {
          value = value.replace(/(\d{2})(\d)/, '($1) $2');
          value = value.replace(/(\d{4})(\d)/, '$1-$2');
        } else {
          value = value.replace(/(\d{2})(\d)/, '($1) $2');
          value = value.replace(/(\d{5})(\d)/, '$1-$2');
        }
        e.target.value = value;
      })
    );
  }

  removeMask() {
    if (this.cpfMaskHandler) {
      this.pixKeyInput.removeEventListener('input', this.cpfMaskHandler);
    }
    if (this.phoneMaskHandler) {
      this.pixKeyInput.removeEventListener('input', this.phoneMaskHandler);
    }
  }

  validatePixKey() {
    const keyType = this.keyTypeSelect.value;
    const value = this.pixKeyInput.value.trim();
    const validation = this.keyValidation;

    if (!value) {
      validation.textContent = '';
      validation.className = 'validation-message';
      return false;
    }

    let isValid = false;
    let message = '';

    switch (keyType) {
      case 'cpf':
        isValid = this.validateCPF(value);
        message = isValid ? 'CPF válido' : 'CPF inválido';
        break;
      case 'phone':
        isValid = this.validatePhone(value);
        message = isValid ? 'Telefone válido' : 'Telefone inválido';
        break;
      case 'email':
        isValid = this.validateEmail(value);
        message = isValid ? 'Email válido' : 'Email inválido';
        break;
      case 'random':
        isValid = value.length >= 10;
        message = isValid
          ? 'Chave válida'
          : 'Chave deve ter pelo menos 10 caracteres';
        break;
    }

    validation.textContent = message;
    validation.className = `validation-message ${isValid ? 'success' : 'error'}`;

    return isValid;
  }

  validateCPF(cpf) {
    cpf = cpf.replace(/\D/g, '');

    if (cpf.length !== 11) return false;
    if (/^(\d)\1{10}$/.test(cpf)) return false;

    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cpf.charAt(i)) * (10 - i);
    }
    let digit1 = ((sum * 10) % 11) % 10;

    if (digit1 !== parseInt(cpf.charAt(9))) return false;

    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cpf.charAt(i)) * (11 - i);
    }
    let digit2 = ((sum * 10) % 11) % 10;

    return digit2 === parseInt(cpf.charAt(10));
  }

  validatePhone(phone) {
    const digits = phone.replace(/\D/g, '');
    return digits.length === 10 || digits.length === 11;
  }

  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  updateCharCounter(input, maxLength) {
    const counter = input.parentElement.querySelector('.char-counter');
    if (counter) {
      counter.textContent = `${input.value.length}/${maxLength} caracteres`;
    }
  }

  async handleFormSubmit(e) {
    e.preventDefault();

    if (!this.validateForm()) {
      return;
    }

    this.setLoading(true);

    try {
      const formData = this.getFormData();
      const brCode = await this.generateBRCode(formData);
      await this.generateAndDisplayQRCode(brCode, formData);

      this.displayResult(brCode, formData);
      this.showToast('QR Code PIX gerado com sucesso!', 'success');
    } catch (error) {
      console.error('Error generating QR Code:', error);
      this.showError('Erro ao gerar QR Code: ' + error.message);
    } finally {
      this.setLoading(false);
    }
  }

  validateForm() {
    const requiredFields = [
      { element: this.pixKeyInput, name: 'Chave PIX' },
      { element: this.receiverNameInput, name: 'Nome do recebedor' },
      { element: this.receiverCityInput, name: 'Cidade do recebedor' },
    ];

    for (const field of requiredFields) {
      if (!field.element.value.trim()) {
        this.showError(`O campo "${field.name}" é obrigatório.`);
        field.element.focus();
        return false;
      }
    }

    // Validate PIX key
    if (!this.validatePixKey()) {
      this.showError('Por favor, insira uma chave PIX válida.');
      this.pixKeyInput.focus();
      return false;
    }

    return true;
  }

  getFormData() {
    // Parse amount
    let amount = 0;
    if (this.amountInput.value.trim()) {
      const amountStr = this.amountInput.value
        .replace(/[^\d,]/g, '')
        .replace(',', '.');
      amount = parseFloat(amountStr) || 0;
    }

    // Clean PIX key based on type
    let pixKey = this.pixKeyInput.value.trim();
    const keyType = this.keyTypeSelect.value;

    if (keyType === 'cpf') {
      pixKey = pixKey.replace(/\D/g, '');
    } else if (keyType === 'phone') {
      pixKey = pixKey.replace(/\D/g, '');
      if (!pixKey.startsWith('55')) {
        pixKey = '55' + pixKey;
      }
      if (!pixKey.startsWith('+')) {
        pixKey = '+' + pixKey;
      }
    }

    return {
      keyType,
      pixKey,
      receiverName: this.receiverNameInput.value.trim(),
      receiverCity: this.receiverCityInput.value.trim(),
      amount,
      reference: this.referenceInput.value.trim(),
      description: this.descriptionInput.value.trim(),
    };
  }

  // PIX BR Code generation (simplified version)
  async generateBRCode(data) {
    const getValue = (tag, value) => {
      return `${tag}${value.length.toString().padStart(2, '0')}${value}`;
    };

    const formattedText = (text) => {
      return text
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '');
    };

    const crcCompute = (data) => {
      // Simplified CRC16 calculation
      let crc = 0xffff;
      const dataBytes = new TextEncoder().encode(data);

      for (const byte of dataBytes) {
        crc ^= byte << 8;
        for (let i = 0; i < 8; i++) {
          if (crc & 0x8000) {
            crc = (crc << 1) ^ 0x1021;
          } else {
            crc <<= 1;
          }
        }
      }

      return (crc & 0xffff).toString(16).toUpperCase().padStart(4, '0');
    };

    // Build account information
    const basePix = getValue('00', 'br.gov.bcb.pix');
    let infoString = getValue('01', data.pixKey);

    if (data.description) {
      infoString += getValue('02', formattedText(data.description));
    }

    const accountInfo = getValue('26', basePix + infoString);

    // Build additional data field
    const txid = data.reference || '***';
    const additionalData = getValue('62', getValue('05', formattedText(txid)));

    // Build complete BR Code
    let resultString = getValue('00', '01'); // Payload Format Indicator
    resultString += getValue('01', '11'); // Point of Initiation Method (static)
    resultString += accountInfo;
    resultString += getValue('52', '0000'); // Merchant Category Code
    resultString += getValue('53', '986'); // Transaction Currency (BRL)

    if (data.amount > 0) {
      resultString += getValue('54', data.amount.toFixed(2));
    }

    resultString += getValue('58', 'BR'); // Country Code
    resultString += getValue('59', formattedText(data.receiverName)); // Merchant Name
    resultString += getValue('60', formattedText(data.receiverCity)); // Merchant City
    resultString += additionalData;
    resultString += '6304'; // CRC placeholder

    const finalBRCode = resultString + crcCompute(resultString);

    return finalBRCode;
  }

  async generateAndDisplayQRCode(brCode, formData) {
    const customizationActive =
      this.customizationPanel.classList.contains('active');

    // Sempre usar qr-code-styling para consistência visual
    if (window.QRCodeStyling) {
      if (customizationActive) {
        // Use advanced styling with user customizations
        await this.generateStyledQRCode(brCode);
      } else {
        // Use standardized styling for consistency
        await this.generateStandardQRCode(brCode);
      }
    } else {
      // Use simple QR generation if QRCodeStyling library is not available
      console.warn(
        'QRCodeStyling library not available, using simple local QR generation'
      );
      await this.generateSimpleQRCode(brCode);
    }
  }

  async generateStyledQRCode(data) {
    const currentMargin = this.getCurrentMargin();

    // If margin is 0, use alternative generation method
    if (currentMargin === 0) {
      console.log('Using alternative QR generation for zero margin');
      return this.generateZeroMarginQR(data);
    }

    // Get user-specified dimensions for export from unified size control
    const size = parseInt(document.getElementById('qr-size').value) || 300;
    const exportWidth = size;
    const exportHeight = size;

    // Create display options with constrained size (max 350px for display)
    const maxDisplaySize = 350;
    const aspectRatio = exportWidth / exportHeight;
    let displayWidth, displayHeight;

    if (exportWidth >= exportHeight) {
      displayWidth = Math.min(exportWidth, maxDisplaySize);
      displayHeight = displayWidth / aspectRatio;
    } else {
      displayHeight = Math.min(exportHeight, maxDisplaySize);
      displayWidth = displayHeight * aspectRatio;
    }

    // PADRONIZAÇÃO: Usar função auxiliar para configurações consistentes
    const displayOptions = this.getStandardQROptions(
      data,
      Math.round(displayWidth),
      Math.round(displayHeight),
      currentMargin,
      currentMargin <= 1 ? 'canvas' : 'svg' // Use canvas for very small margins
    );

    // PADRONIZAÇÃO: Usar função auxiliar para configurações de exportação consistentes
    this.exportOptions = this.getStandardQROptions(
      data,
      exportWidth,
      exportHeight,
      currentMargin,
      'svg' // Sempre SVG para exportação de alta qualidade
    );

    // Add image if present
    if (this.currentImageFile) {
      const reader = new FileReader();
      return new Promise((resolve) => {
        reader.onload = (e) => {
          displayOptions.image = e.target.result;
          this.exportOptions.image = e.target.result;

          // Add imageOptions for images
          if (!displayOptions.imageOptions) {
            displayOptions.imageOptions = {
              crossOrigin: 'anonymous',
              margin:
                parseInt(document.getElementById('image-margin').value) || 0,
              imageSize:
                parseFloat(document.getElementById('image-size').value) || 0.4,
              hideBackgroundDots: document.getElementById(
                'hide-background-dots'
              ).checked,
            };
          }
          if (!this.exportOptions.imageOptions) {
            this.exportOptions.imageOptions = {
              crossOrigin: 'anonymous',
              margin:
                parseInt(document.getElementById('image-margin').value) || 0,
              imageSize:
                parseFloat(document.getElementById('image-size').value) || 0.4,
              hideBackgroundDots: document.getElementById(
                'hide-background-dots'
              ).checked,
            };
          }

          this.createQRCode(displayOptions);
          resolve();
        };
        reader.readAsDataURL(this.currentImageFile);
      });
    } else {
      this.createQRCode(displayOptions);
    }
  }

  createQRCode(options) {
    console.log('createQRCode called with options:', options);
    console.log('Margin value being used:', options.margin);

    this.qrCode = new window.QRCodeStyling(options);

    // Clear preview
    this.qrPreview.innerHTML = '';

    // Add QR Code
    this.qrCode.append(this.qrPreview);

    // Post-process for zero margin if needed
    if (options.margin === 0) {
      setTimeout(() => this.forceZeroMargin(), 100);
    }

    console.log('QR Code created and appended');
  }

  forceZeroMargin() {
    console.log('forceZeroMargin called');

    // Find the SVG or Canvas element
    const svg = this.qrPreview.querySelector('svg');
    const canvas = this.qrPreview.querySelector('canvas');

    if (svg) {
      console.log('Found SVG, manipulating viewBox and removing margin');
      // Remove any margin/padding from SVG
      svg.style.margin = '0';
      svg.style.padding = '0';

      // Try to adjust viewBox to eliminate internal margin
      const viewBox = svg.getAttribute('viewBox');
      if (viewBox) {
        console.log('Original viewBox:', viewBox);
        const parts = viewBox.split(' ');
        if (parts.length === 4) {
          // Expand the viewBox to crop out margins
          const margin = 5; // Estimated internal margin
          parts[0] = (parseFloat(parts[0]) + margin).toString();
          parts[1] = (parseFloat(parts[1]) + margin).toString();
          parts[2] = (parseFloat(parts[2]) - margin * 2).toString();
          parts[3] = (parseFloat(parts[3]) - margin * 2).toString();
          const newViewBox = parts.join(' ');
          svg.setAttribute('viewBox', newViewBox);
          console.log('New viewBox:', newViewBox);
        }
      }
    }

    if (canvas) {
      console.log('Found Canvas, manipulating margins');
      canvas.style.margin = '0';
      canvas.style.padding = '0';
    }
  }

  async generateStandardQRCode(brCode) {
    try {
      console.log('Generating standard QR code with qr-code-styling library');

      // PADRONIZAÇÃO: Usar função auxiliar para configurações consistentes
      const standardOptions = this.getStandardQROptions(
        brCode,
        300, // Largura padrão
        300, // Altura padrão
        10, // Margem padrão consistente
        'svg' // SVG para melhor qualidade
      );

      // Sobrescrever com configurações específicas para QR padrão
      standardOptions.dotsOptions.type = 'square'; // Sempre quadrado para padrão
      standardOptions.dotsOptions.color = '#000000'; // Sempre preto para padrão
      standardOptions.backgroundOptions.color = '#ffffff'; // Sempre branco para padrão
      standardOptions.cornersSquareOptions.type = undefined; // Usar padrão da biblioteca
      standardOptions.cornersSquareOptions.color = '#000000';
      standardOptions.cornersDotOptions.type = undefined; // Usar padrão da biblioteca
      standardOptions.cornersDotOptions.color = '#000000';

      // Remover imageOptions para QR padrão (sem imagem)
      delete standardOptions.imageOptions;

      // Criar QR Code com configurações padronizadas
      this.qrCode = new window.QRCodeStyling(standardOptions);

      // Armazenar opções de exportação (mesmo que as de display para simplicidade)
      this.exportOptions = { ...standardOptions };

      // Limpar preview e adicionar QR Code
      this.qrPreview.innerHTML = '';
      this.qrCode.append(this.qrPreview);

      console.log('Standard QR code generated successfully');
    } catch (error) {
      console.error('Standard QR Code generation error:', error);
      throw new Error('Não foi possível gerar o QR Code. Tente novamente.');
    }
  }

  async generateSimpleQRCode(brCode) {
    try {
      console.log('Using local QR code generation');

      // Generate QR code using local implementation
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Set canvas size
      const size = 300;
      canvas.width = size;
      canvas.height = size;

      // Generate QR code using a simple implementation
      const qrCode = this.createQRMatrix(brCode);
      const moduleSize = Math.floor(size / qrCode.length);
      const offset = (size - qrCode.length * moduleSize) / 2;

      // Clear canvas with white background
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, size, size);

      // Draw QR code modules
      ctx.fillStyle = '#000000';
      for (let row = 0; row < qrCode.length; row++) {
        for (let col = 0; col < qrCode[row].length; col++) {
          if (qrCode[row][col]) {
            ctx.fillRect(
              offset + col * moduleSize,
              offset + row * moduleSize,
              moduleSize,
              moduleSize
            );
          }
        }
      }

      // Convert canvas to data URL and display
      const imageUrl = canvas.toDataURL('image/png');
      this.qrPreview.innerHTML = `<img src="${imageUrl}" alt="QR Code PIX" style="max-width: 100%; border-radius: 8px;">`;
    } catch (error) {
      console.error('QR Code generation error:', error);
      throw new Error('Não foi possível gerar o QR Code. Tente novamente.');
    }
  }

  async generateZeroMarginQR(data) {
    console.log('generateZeroMarginQR called - using optimized QR generation');

    try {
      // OPTIMIZATION: Try hybrid approach first - use margin=1 with CSS cropping for better quality
      const useHybridApproach = this.shouldUseHybridApproach();

      if (useHybridApproach) {
        console.log('Using hybrid approach (margin=1 + CSS cropping)');
        await this.generateHybridZeroMarginQR(data);
      } else {
        console.log('Using native zero margin processing');
        // Use native QR generation with zero margin
        const displayCanvas = await this.createNativeZeroMarginQR(data);

        // Clear preview and add the zero-margin QR code
        this.qrPreview.innerHTML = '';
        this.qrPreview.appendChild(displayCanvas);

        // Store canvases for download (export canvas is created in processQRWithAllOptions)
        this.qrCode = null; // Clear the QRCodeStyling instance
        // zeroMarginExportCanvas is already stored in processQRWithAllOptions
        this.zeroMarginCanvas = this.zeroMarginExportCanvas; // Use export canvas for download

        console.log('Native zero margin QR code generated successfully');
        console.log(
          'Export canvas dimensions:',
          this.zeroMarginExportCanvas
            ? `${this.zeroMarginExportCanvas.width}x${this.zeroMarginExportCanvas.height}`
            : 'not found'
        );
      }
    } catch (error) {
      console.error('Zero margin QR generation error:', error);
      // Fallback to styled QR with margin 1
      console.log('Falling back to margin 1');
      const fallbackData = data;
      this.generateStyledQRCodeWithFallback(fallbackData, 1);
    }
  }

  shouldUseHybridApproach() {
    // MELHORADO: Lógica mais inteligente para escolha do método
    // Use hybrid approach when:
    // 1. No custom image is being used (simpler case)
    // 2. Standard dot types are being used
    // 3. Target size is reasonable for CSS cropping
    // 4. Background color is standard (white or light colors)

    const hasCustomImage = this.currentImageFile !== null;
    const dotType = document.getElementById('dots-type').value;
    const targetWidth =
      parseInt(document.getElementById('qr-size').value) || 300;
    const backgroundColor =
      document.getElementById('background-color').value || '#ffffff';

    const isStandardDotType = ['square', 'rounded', 'dots'].includes(dotType);
    const isReasonableSize = targetWidth >= 200 && targetWidth <= 800; // Increased upper limit
    const isLightBackground = this.isLightColor(backgroundColor);

    // FALLBACK: Se a detecção de bordas falhou anteriormente, prefira o método híbrido
    const preferHybrid =
      !hasCustomImage &&
      isStandardDotType &&
      isReasonableSize &&
      isLightBackground;

    console.log('shouldUseHybridApproach decision:', {
      hasCustomImage,
      dotType,
      targetWidth,
      backgroundColor,
      isStandardDotType,
      isReasonableSize,
      isLightBackground,
      preferHybrid,
    });

    return preferHybrid;
  }

  // Função auxiliar para determinar se uma cor é clara
  isLightColor(hexColor) {
    const rgb = this.hexToRgb(hexColor);
    if (!rgb) return true; // Default to light if can't parse

    // Calculate luminance using standard formula
    const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
    return luminance > 0.7; // Consider light if luminance > 70%
  }

  // Função auxiliar para converter hex para RGB
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  }

  // IMPROVED: Precise QR bounds detection algorithm
  findPreciseQRBounds(imageData, width, height, bgColor) {
    let minX = width,
      minY = height,
      maxX = 0,
      maxY = 0;
    let foundPixels = false;

    // Multi-pass detection with different tolerances
    const tolerances = [5, 15, 30, 50]; // Progressive tolerance levels

    for (const tolerance of tolerances) {
      minX = width;
      minY = height;
      maxX = 0;
      maxY = 0;
      foundPixels = false;

      // Scan all pixels
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const idx = (y * width + x) * 4;
          const r = imageData.data[idx];
          const g = imageData.data[idx + 1];
          const b = imageData.data[idx + 2];
          const a = imageData.data[idx + 3];

          // Enhanced pixel detection
          const colorDistance = Math.sqrt(
            Math.pow(r - bgColor.r, 2) +
              Math.pow(g - bgColor.g, 2) +
              Math.pow(b - bgColor.b, 2)
          );

          // Check if pixel is significantly different from background
          if (a > 200 && colorDistance > tolerance) {
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            maxX = Math.max(maxX, x);
            maxY = Math.max(maxY, y);
            foundPixels = true;
          }
        }
      }

      // If we found valid bounds, break
      if (foundPixels && minX < maxX && minY < maxY) {
        // Additional validation: ensure bounds are reasonable
        const qrWidth = maxX - minX + 1;
        const qrHeight = maxY - minY + 1;
        const aspectRatio = qrWidth / qrHeight;

        // QR codes should be roughly square (aspect ratio between 0.8 and 1.2)
        if (aspectRatio >= 0.8 && aspectRatio <= 1.2) {
          console.log(`Precise bounds found with tolerance ${tolerance}:`, {
            minX,
            minY,
            maxX,
            maxY,
            width: qrWidth,
            height: qrHeight,
            aspectRatio,
          });

          return { minX, minY, maxX, maxY };
        }
      }
    }

    console.warn('Could not find precise QR bounds with any tolerance');
    return null;
  }

  async generateHybridZeroMarginQR(data) {
    // IMPROVED: Direct zero-margin generation instead of CSS scaling
    console.log('Generating improved hybrid zero-margin QR code');

    // Get user-specified dimensions for export from unified size control
    const size = parseInt(document.getElementById('qr-size').value) || 300;
    const exportWidth = size;
    const exportHeight = size;

    // Create display options with constrained size (max 350px for display)
    const maxDisplaySize = 350;
    const aspectRatio = exportWidth / exportHeight;
    let displayWidth, displayHeight;

    if (exportWidth >= exportHeight) {
      displayWidth = Math.min(exportWidth, maxDisplaySize);
      displayHeight = displayWidth / aspectRatio;
    } else {
      displayHeight = Math.min(exportHeight, maxDisplaySize);
      displayWidth = displayHeight * aspectRatio;
    }

    // IMPROVED: Generate with higher resolution then crop precisely
    const oversampleFactor = 2; // Generate at 2x resolution for better quality
    const tempWidth = Math.round(displayWidth * oversampleFactor);
    const tempHeight = Math.round(displayHeight * oversampleFactor);

    // PADRONIZAÇÃO: Usar função auxiliar para configurações consistentes
    const tempOptions = this.getStandardQROptions(
      data,
      tempWidth,
      tempHeight,
      Math.max(2, Math.round(tempWidth * 0.03)), // Proportional margin for cropping
      'canvas' // Canvas para melhor controle
    );

    // Create temporary QR code at higher resolution
    const tempQR = new window.QRCodeStyling(tempOptions);

    // Create temporary container
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '-9999px';
    document.body.appendChild(tempContainer);

    // Render the QR code
    tempQR.append(tempContainer);
    await new Promise((resolve) => setTimeout(resolve, 100));

    const tempCanvas = tempContainer.querySelector('canvas');
    if (!tempCanvas) {
      document.body.removeChild(tempContainer);
      throw new Error('Failed to generate temporary canvas');
    }

    // Create final display canvas with precise cropping
    const displayCanvas = await this.createPreciseCroppedCanvas(
      tempCanvas,
      Math.round(displayWidth),
      Math.round(displayHeight)
    );

    // Clear preview and add the zero-margin QR code
    this.qrPreview.innerHTML = '';
    this.qrPreview.appendChild(displayCanvas);

    // Clean up temporary container
    document.body.removeChild(tempContainer);

    // Store for download - create export version with same technique
    this.zeroMarginCanvas = await this.createPreciseExportCanvas(
      data,
      exportWidth,
      exportHeight
    );

    console.log('Improved hybrid zero-margin QR code generated successfully');
  }

  // IMPROVED: Create precisely cropped canvas from high-resolution source
  async createPreciseCroppedCanvas(sourceCanvas, targetWidth, targetHeight) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    // Set target dimensions
    canvas.width = targetWidth;
    canvas.height = targetHeight;

    // Disable smoothing for crisp pixels
    ctx.imageSmoothingEnabled = false;
    ctx.mozImageSmoothingEnabled = false;
    ctx.webkitImageSmoothingEnabled = false;
    ctx.msImageSmoothingEnabled = false;

    // Get background color
    const backgroundColor =
      document.getElementById('background-color').value || '#ffffff';
    const bgColor = this.hexToRgb(backgroundColor);

    // Analyze source canvas to find QR code boundaries
    const sourceCtx = sourceCanvas.getContext('2d');
    const imageData = sourceCtx.getImageData(
      0,
      0,
      sourceCanvas.width,
      sourceCanvas.height
    );

    // Find precise boundaries using improved algorithm
    const bounds = this.findPreciseQRBounds(
      imageData,
      sourceCanvas.width,
      sourceCanvas.height,
      bgColor
    );

    if (!bounds) {
      // Fallback: use proportional margins
      const margin = Math.round(sourceCanvas.width * 0.05);
      bounds = {
        minX: margin,
        minY: margin,
        maxX: sourceCanvas.width - margin - 1,
        maxY: sourceCanvas.height - margin - 1,
      };
    }

    const qrWidth = bounds.maxX - bounds.minX + 1;
    const qrHeight = bounds.maxY - bounds.minY + 1;

    // Fill background
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, targetWidth, targetHeight);

    // Calculate scaling to fit target size while maintaining aspect ratio
    const scaleX = targetWidth / qrWidth;
    const scaleY = targetHeight / qrHeight;
    const scale = Math.min(scaleX, scaleY);

    // Center the QR code
    const scaledWidth = qrWidth * scale;
    const scaledHeight = qrHeight * scale;
    const offsetX = Math.round((targetWidth - scaledWidth) / 2);
    const offsetY = Math.round((targetHeight - scaledHeight) / 2);

    // Draw the precisely cropped QR code
    ctx.drawImage(
      sourceCanvas,
      bounds.minX,
      bounds.minY,
      qrWidth,
      qrHeight,
      offsetX,
      offsetY,
      scaledWidth,
      scaledHeight
    );

    return canvas;
  }

  // IMPROVED: Create precise export canvas
  async createPreciseExportCanvas(data, width, height) {
    // Generate at higher resolution for better quality
    const oversampleFactor = Math.max(2, Math.min(4, Math.round(width / 150))); // Adaptive oversampling
    const tempWidth = width * oversampleFactor;
    const tempHeight = height * oversampleFactor;

    // PADRONIZAÇÃO: Usar função auxiliar para configurações consistentes
    const tempOptions = this.getStandardQROptions(
      data,
      tempWidth,
      tempHeight,
      Math.max(4, Math.round(tempWidth * 0.03)), // Proportional margin for cropping
      'canvas'
    );

    // Create temporary QR code for export
    const tempQR = new window.QRCodeStyling(tempOptions);

    // Create temporary container
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '-9999px';
    document.body.appendChild(tempContainer);

    // Render the QR code
    tempQR.append(tempContainer);
    await new Promise((resolve) => setTimeout(resolve, 150));

    const tempCanvas = tempContainer.querySelector('canvas');
    if (!tempCanvas) {
      document.body.removeChild(tempContainer);
      throw new Error('Failed to generate export canvas');
    }

    // Create final export canvas with precise cropping
    const exportCanvas = await this.createPreciseCroppedCanvas(
      tempCanvas,
      width,
      height
    );

    // Clean up
    document.body.removeChild(tempContainer);

    return exportCanvas;
  }

  // ALGORITMO MELHORADO: Função para criar QR code nativo com margem zero
  async createNativeZeroMarginQR(data) {
    // Get user-specified dimensions for export from unified size control
    const size = parseInt(document.getElementById('qr-size').value) || 300;
    const exportWidth = size;
    const exportHeight = size;

    // Create display options with constrained size (max 350px for display)
    const maxDisplaySize = 350;
    const aspectRatio = exportWidth / exportHeight;
    let displayWidth, displayHeight;

    if (exportWidth >= exportHeight) {
      displayWidth = Math.min(exportWidth, maxDisplaySize);
      displayHeight = displayWidth / aspectRatio;
    } else {
      displayHeight = Math.min(exportHeight, maxDisplaySize);
      displayWidth = displayHeight * aspectRatio;
    }

    // PADRONIZAÇÃO: Usar função auxiliar para configurações consistentes
    const displayOptions = this.getStandardQROptions(
      data,
      Math.round(displayWidth),
      Math.round(displayHeight),
      0, // Margem zero
      'canvas' // Canvas para margem zero
    );

    // PADRONIZAÇÃO: Usar função auxiliar para configurações de exportação consistentes
    const exportOptions = this.getStandardQROptions(
      data,
      exportWidth,
      exportHeight,
      0, // Margem zero
      'canvas' // Canvas para margem zero
    );

    // Process both display and export versions
    const displayCanvas = await this.createZeroMarginCanvas(displayOptions);
    this.zeroMarginExportCanvas =
      await this.createZeroMarginCanvas(exportOptions);

    return displayCanvas;
  }

  // ALGORITMO MELHORADO: Função para criar canvas com margem zero e detecção de bordas otimizada
  async createZeroMarginCanvas(options) {
    // Create a temporary QRCodeStyling instance with all personalizations
    const tempQR = new window.QRCodeStyling(options);

    // Create a temporary container to render the QR code
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '-9999px';
    document.body.appendChild(tempContainer);

    // Render the QR code
    tempQR.append(tempContainer);

    // Wait for rendering to complete
    await new Promise((resolve) => setTimeout(resolve, 150));

    // Get the canvas from the temporary container
    const tempCanvas = tempContainer.querySelector('canvas');
    if (!tempCanvas) {
      document.body.removeChild(tempContainer);
      throw new Error('Failed to generate QR code canvas');
    }

    // Create our final canvas with exact QR dimensions (no margin)
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    // OPTIMIZATION: Disable image smoothing for crisp pixels
    ctx.imageSmoothingEnabled = false;
    ctx.mozImageSmoothingEnabled = false;
    ctx.webkitImageSmoothingEnabled = false;
    ctx.msImageSmoothingEnabled = false;

    // Calculate the actual QR code area (removing any internal margin)
    const sourceCanvas = tempCanvas;
    const sourceCtx = sourceCanvas.getContext('2d');
    const imageData = sourceCtx.getImageData(
      0,
      0,
      sourceCanvas.width,
      sourceCanvas.height
    );

    // Find the actual QR code boundaries by scanning for non-background pixels
    const backgroundColor =
      document.getElementById('background-color').value || '#FFFFFF';
    const bgColor = this.hexToRgb(backgroundColor);

    let minX = sourceCanvas.width,
      minY = sourceCanvas.height;
    let maxX = 0,
      maxY = 0;

    // IMPROVED: More precise edge detection with adaptive tolerance
    const baseTolerance = 15; // Reduced from 30 for better precision
    let tolerance = baseTolerance;

    // First pass: try with strict tolerance
    let foundPixels = false;
    for (let pass = 0; pass < 2 && !foundPixels; pass++) {
      minX = sourceCanvas.width;
      minY = sourceCanvas.height;
      maxX = 0;
      maxY = 0;

      for (let y = 0; y < sourceCanvas.height; y++) {
        for (let x = 0; x < sourceCanvas.width; x++) {
          const idx = (y * sourceCanvas.width + x) * 4;
          const r = imageData.data[idx];
          const g = imageData.data[idx + 1];
          const b = imageData.data[idx + 2];
          const a = imageData.data[idx + 3];

          // Enhanced pixel detection: check alpha channel and use better color distance
          const colorDistance = Math.sqrt(
            Math.pow(r - bgColor.r, 2) +
              Math.pow(g - bgColor.g, 2) +
              Math.pow(b - bgColor.b, 2)
          );

          // Check if pixel is not background color (improved detection)
          if (a > 200 && colorDistance > tolerance) {
            // Increased alpha threshold from 128 to 200
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            maxX = Math.max(maxX, x);
            maxY = Math.max(maxY, y);
            foundPixels = true;
          }
        }
      }

      // If no pixels found in first pass, increase tolerance and try again
      if (!foundPixels) {
        tolerance = baseTolerance * 2;
        console.log('Edge detection: increasing tolerance to', tolerance);
      }
    }

    // SAFETY: Ensure we found valid boundaries with improved fallback
    if (minX >= maxX || minY >= maxY || !foundPixels) {
      console.warn(
        'Could not detect QR code boundaries, using intelligent fallback'
      );

      // Use a more intelligent fallback: assume 10% margin on each side
      const marginPercent = 0.1;
      const marginX = Math.floor(sourceCanvas.width * marginPercent);
      const marginY = Math.floor(sourceCanvas.height * marginPercent);

      minX = marginX;
      minY = marginY;
      maxX = sourceCanvas.width - marginX - 1;
      maxY = sourceCanvas.height - marginY - 1;

      console.log(
        `Using fallback boundaries: ${minX},${minY} to ${maxX},${maxY}`
      );
    }

    // Set canvas size to the actual QR code dimensions
    const qrWidth = maxX - minX + 1;
    const qrHeight = maxY - minY + 1;

    // IMPROVEMENT: Use pixel-perfect scaling when possible
    const targetWidth = options.width;
    const targetHeight = options.height;

    // Calculate optimal scale to maintain crisp pixels
    const scaleX = targetWidth / qrWidth;
    const scaleY = targetHeight / qrHeight;
    const scale = Math.min(scaleX, scaleY);

    // Use integer scaling when possible for maximum sharpness
    const integerScale = Math.floor(scale);
    const useIntegerScale = integerScale >= 1 && scale - integerScale < 0.15; // Slightly more lenient
    const finalScale = useIntegerScale ? integerScale : scale;

    canvas.width = targetWidth;
    canvas.height = targetHeight;

    // Center the QR code in the canvas
    const scaledWidth = qrWidth * finalScale;
    const scaledHeight = qrHeight * finalScale;
    const offsetX = Math.round((targetWidth - scaledWidth) / 2);
    const offsetY = Math.round((targetHeight - scaledHeight) / 2);

    // Fill background with exact color
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, targetWidth, targetHeight);

    // OPTIMIZATION: Always use high-quality rendering for zero margin
    ctx.save();
    ctx.imageSmoothingEnabled = false;
    ctx.imageSmoothingQuality = 'high';

    // Use drawImage with precise positioning
    ctx.drawImage(
      sourceCanvas,
      minX,
      minY,
      qrWidth,
      qrHeight,
      offsetX,
      offsetY,
      scaledWidth,
      scaledHeight
    );
    ctx.restore();

    // Clean up
    document.body.removeChild(tempContainer);

    return canvas;
  }

  // Função auxiliar para fallback quando a geração com margem zero falha
  async generateStyledQRCodeWithFallback(data, fallbackMargin = 1) {
    console.log(`Generating QR code with fallback margin: ${fallbackMargin}`);

    // Get user-specified dimensions for export from unified size control
    const size = parseInt(document.getElementById('qr-size').value) || 300;
    const exportWidth = size;
    const exportHeight = size;

    // Create display options with constrained size (max 350px for display)
    const maxDisplaySize = 350;
    const aspectRatio = exportWidth / exportHeight;
    let displayWidth, displayHeight;

    if (exportWidth >= exportHeight) {
      displayWidth = Math.min(exportWidth, maxDisplaySize);
      displayHeight = displayWidth / aspectRatio;
    } else {
      displayHeight = Math.min(exportHeight, maxDisplaySize);
      displayWidth = displayHeight * aspectRatio;
    }

    // PADRONIZAÇÃO: Usar função auxiliar para configurações consistentes
    const displayOptions = this.getStandardQROptions(
      data,
      Math.round(displayWidth),
      Math.round(displayHeight),
      fallbackMargin,
      'canvas'
    );

    // Create QR code with fallback margin
    this.qrCode = new window.QRCodeStyling(displayOptions);

    // Clear preview
    this.qrPreview.innerHTML = '';

    // Add QR Code
    this.qrCode.append(this.qrPreview);

    // Store export options
    this.exportOptions = this.getStandardQROptions(
      data,
      exportWidth,
      exportHeight,
      fallbackMargin,
      'svg'
    );

    console.log(
      `Fallback QR code generated successfully with margin: ${fallbackMargin}`
    );
  }

  generateQRIfReady() {
    // Only regenerate if QR is already displayed and form is valid
    if (this.qrResult.style.display !== 'none' && this.currentBRCode) {
      const customizationActive =
        this.customizationPanel.classList.contains('active');

      // Clear any existing QR code instances to prevent conflicts with new optimizations
      this.qrCode = null;
      this.zeroMarginCanvas = null;
      this.zeroMarginExportCanvas = null;
      this.zeroMarginDisplayCanvas = null;

      // Sempre usar qr-code-styling para consistência visual
      if (window.QRCodeStyling) {
        if (customizationActive) {
          // Use styled QR code generation with user customizations
          this.generateStyledQRCode(this.currentBRCode);
        } else {
          // Use standardized QR code generation
          this.generateStandardQRCode(this.currentBRCode);
        }
      } else {
        // Use simple QR generation if QRCodeStyling library is not available
        console.warn(
          'QRCodeStyling library not available, using simple local QR generation'
        );
        this.generateSimpleQRCode(this.currentBRCode);
      }
    }
  }

  displayResult(brCode, formData) {
    this.currentBRCode = brCode;

    // Hide placeholder and show result
    this.qrPlaceholder.style.display = 'none';
    this.qrResult.style.display = 'block';

    // Set BR Code text
    this.brCodeText.textContent = brCode;

    // Display PIX details
    this.displayPixDetails(formData);

    // Scroll to result
    this.qrResult.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }

  displayPixDetails(data) {
    const details = [
      { label: 'Recebedor', value: data.receiverName },
      { label: 'Cidade', value: data.receiverCity },
      { label: 'Chave PIX', value: data.pixKey },
      {
        label: 'Valor',
        value:
          data.amount > 0
            ? `R$ ${data.amount.toFixed(2).replace('.', ',')}`
            : 'Valor livre',
      },
    ];

    if (data.reference) {
      details.push({ label: 'Referência', value: data.reference });
    }

    if (data.description) {
      details.push({ label: 'Descrição', value: data.description });
    }

    const detailsHTML = details
      .map(
        (item) => `
                <div class="pix-detail-item">
                    <span class="pix-detail-label">${item.label}:</span>
                    <span class="pix-detail-value">${item.value}</span>
                </div>
            `
      )
      .join('');

    this.pixDetails.innerHTML = detailsHTML;
  }

  downloadQRCode(format) {
    try {
      console.log('Download requested for format:', format);

      // Handle zero margin download with native canvas (only for customized QR codes)
      if (this.zeroMarginCanvas && this.getCurrentMargin() === 0) {
        const link = document.createElement('a');
        link.download = `qrcode-pix-zero-margin.${format === 'svg' ? 'png' : format}`;

        // Convert canvas to data URL
        const dataURL = this.zeroMarginCanvas.toDataURL(
          `image/${format === 'svg' ? 'png' : format}`
        );
        link.href = dataURL;
        link.click();
        this.showToast(
          `QR Code baixado como ${format.toUpperCase()}!`,
          'success'
        );
        return;
      }

      // Handle QR code download using qr-code-styling library
      if (this.qrCode && typeof window.QRCodeStyling !== 'undefined') {
        let filename = 'qrcode-pix';

        // Use export options if available (for customized QR codes)
        if (this.exportOptions) {
          // Create export version with user-specified dimensions
          const exportQR = new window.QRCodeStyling({
            ...this.exportOptions,
            type: format === 'svg' ? 'svg' : 'canvas',
          });

          filename = 'qrcode-pix-personalizado';
          exportQR.download({
            name: filename,
            extension: format,
          });
        } else {
          // Use standard QR code for download (both standard and customized without export options)
          filename = 'qrcode-pix-padrao';
          this.qrCode.download({
            name: filename,
            extension: format,
          });
        }
      } else {
        // Handle simple QR code (image) download from local generation
        const img = this.qrPreview.querySelector('img');
        if (img) {
          const link = document.createElement('a');
          link.download = `qrcode-pix-simples.${format === 'svg' ? 'png' : format}`;
          link.href = img.src;
          link.click();
        } else {
          throw new Error('Nenhum QR Code encontrado para download');
        }
      }

      this.showToast(
        `QR Code baixado como ${format.toUpperCase()}!`,
        'success'
      );
    } catch (error) {
      console.error('Download error:', error);
      this.showError('Erro ao fazer download do QR Code.');
    }
  }

  createQRMatrix(data) {
    // Simple QR code matrix generation
    // This is a simplified implementation for demonstration
    // In a real implementation, you would use a proper QR code library

    try {
      // Use a basic QR code generation approach
      // For now, we'll create a simple pattern that represents the data
      const size = 25; // QR code modules
      const matrix = Array(size)
        .fill()
        .map(() => Array(size).fill(false));

      // Add finder patterns (corners)
      this.addFinderPattern(matrix, 0, 0);
      this.addFinderPattern(matrix, 0, size - 7);
      this.addFinderPattern(matrix, size - 7, 0);

      // Add timing patterns
      for (let i = 8; i < size - 8; i++) {
        matrix[6][i] = i % 2 === 0;
        matrix[i][6] = i % 2 === 0;
      }

      // Add data modules (simplified pattern based on data hash)
      const hash = this.simpleHash(data);
      for (let row = 0; row < size; row++) {
        for (let col = 0; col < size; col++) {
          if (!this.isReservedModule(row, col, size)) {
            matrix[row][col] = (hash + row + col) % 3 === 0;
          }
        }
      }

      return matrix;
    } catch (error) {
      console.error('Error creating QR matrix:', error);
      // Return a simple fallback pattern
      return this.createFallbackMatrix();
    }
  }

  addFinderPattern(matrix, startRow, startCol) {
    const pattern = [
      [1, 1, 1, 1, 1, 1, 1],
      [1, 0, 0, 0, 0, 0, 1],
      [1, 0, 1, 1, 1, 0, 1],
      [1, 0, 1, 1, 1, 0, 1],
      [1, 0, 1, 1, 1, 0, 1],
      [1, 0, 0, 0, 0, 0, 1],
      [1, 1, 1, 1, 1, 1, 1],
    ];

    for (let row = 0; row < 7; row++) {
      for (let col = 0; col < 7; col++) {
        if (
          startRow + row < matrix.length &&
          startCol + col < matrix[0].length
        ) {
          matrix[startRow + row][startCol + col] = pattern[row][col] === 1;
        }
      }
    }
  }

  isReservedModule(row, col, size) {
    // Check if module is part of finder patterns
    if (
      (row < 9 && col < 9) ||
      (row < 9 && col >= size - 8) ||
      (row >= size - 8 && col < 9)
    ) {
      return true;
    }

    // Check if module is part of timing patterns
    if (row === 6 || col === 6) {
      return true;
    }

    return false;
  }

  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  createFallbackMatrix() {
    const size = 25;
    const matrix = Array(size)
      .fill()
      .map(() => Array(size).fill(false));

    // Create a simple checkerboard pattern
    for (let row = 0; row < size; row++) {
      for (let col = 0; col < size; col++) {
        matrix[row][col] = (row + col) % 2 === 0;
      }
    }

    return matrix;
  }

  async copyBRCode() {
    if (!this.currentBRCode) return;

    try {
      await navigator.clipboard.writeText(this.currentBRCode);
      this.showToast(
        'BR Code copiado para a área de transferência!',
        'success'
      );
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = this.currentBRCode;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);

      this.showToast('BR Code copiado!', 'success');
    }
  }

  setLoading(loading) {
    this.generateBtn.disabled = loading;
    this.generateBtn.classList.toggle('loading', loading);

    if (loading) {
      this.loadingSpinner.style.display = 'block';
    } else {
      this.loadingSpinner.style.display = 'none';
    }
  }

  showError(message) {
    this.errorMessage.textContent = message;
    this.errorModal.style.display = 'block';
  }

  hideModal() {
    this.errorModal.style.display = 'none';
  }

  showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;

    document.body.appendChild(toast);

    // Show toast
    setTimeout(() => toast.classList.add('show'), 100);

    // Hide toast
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
  }
}

// Preset functions (from original qr-generator.html)
function applyPreset(presetName) {
  const presets = {
    modern: {
      dotsType: 'rounded',
      dotsColor: '#667eea',
      cornerSquareType: 'extra-rounded',
      cornerSquareColor: '#764ba2',
      cornerDotType: 'rounded',
      cornerDotColor: '#667eea',
      backgroundColor: '#ffffff',
    },
    classic: {
      dotsType: 'square',
      dotsColor: '#000000',
      cornerSquareType: 'square',
      cornerSquareColor: '#000000',
      cornerDotType: 'square',
      cornerDotColor: '#000000',
      backgroundColor: '#ffffff',
    },
    elegant: {
      dotsType: 'classy-rounded',
      dotsColor: '#2c3e50',
      cornerSquareType: 'rounded',
      cornerSquareColor: '#34495e',
      cornerDotType: 'rounded',
      cornerDotColor: '#2c3e50',
      backgroundColor: '#ecf0f1',
    },
    vibrant: {
      dotsType: 'dots',
      dotsColor: '#e74c3c',
      cornerSquareType: 'extra-rounded',
      cornerSquareColor: '#3498db',
      cornerDotType: 'dot',
      cornerDotColor: '#f39c12',
      backgroundColor: '#ffffff',
    },
    circular: {
      dotsType: 'rounded',
      dotsColor: '#2c3e50',
      cornerSquareType: 'dot',
      cornerSquareColor: '#e74c3c',
      cornerDotType: 'dot',
      cornerDotColor: '#3498db',
      backgroundColor: '#ffffff',
    },
  };

  const preset = presets[presetName];
  if (preset) {
    document.getElementById('dots-type').value = preset.dotsType;
    document.getElementById('dots-color').value = preset.dotsColor;
    document.getElementById('corner-square-type').value =
      preset.cornerSquareType;
    document.getElementById('corner-square-color').value =
      preset.cornerSquareColor;
    document.getElementById('corner-dot-type').value = preset.cornerDotType;
    document.getElementById('corner-dot-color').value = preset.cornerDotColor;
    document.getElementById('background-color').value = preset.backgroundColor;

    // Regenerate QR if already displayed
    if (window.pixGenerator && window.pixGenerator.currentBRCode) {
      window.pixGenerator.generateQRIfReady();
    }
  }
}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.pixGenerator = new PixQRGeneratorIntegrated();
});
